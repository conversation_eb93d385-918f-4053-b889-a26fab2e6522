{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaUsers, FaUserPlus, FaUserCheck, FaUserClock, FaEdit, FaSearch, FaEye, FaUserSlash, FaBan, FaTrophy, FaCoins, FaChartLine, FaCalendarAlt } from 'react-icons/fa';\nimport './UserManagement.css';\n// import CustomModal from '../components/CustomModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction UserManagement() {\n  _s();\n  var _selectedUserDetails$;\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [editingUserId, setEditingUserId] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [usersPerPage] = useState(10);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    newUsers: 0,\n    pendingUsers: 0\n  });\n  const [editingUser, setEditingUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    favorite_team: '',\n    balance: 0\n  });\n  const [showAddUserModal, setShowAddUserModal] = useState(false);\n  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);\n  const [selectedUserDetails, setSelectedUserDetails] = useState(null);\n  const [loadingUserDetails, setLoadingUserDetails] = useState(false);\n  const [newUser, setNewUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    password: '',\n    favorite_team: '',\n    balance: 0\n  });\n\n  // Modal states - temporarily disabled\n  // const [modalState, setModalState] = useState({\n  //     isOpen: false,\n  //     type: 'confirm',\n  //     title: '',\n  //     message: '',\n  //     onConfirm: null,\n  //     confirmText: 'Confirm',\n  //     confirmButtonColor: 'blue'\n  // });\n\n  useEffect(() => {\n    fetchUsers();\n    fetchTeams();\n  }, []);\n  const fetchUsers = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php`);\n      if (response.data.success) {\n        const userData = response.data.data || [];\n        setUsers(userData);\n\n        // Calculate stats\n        const now = new Date();\n        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n\n        // For demo purposes, we'll simulate some stats\n        const totalUsers = userData.length;\n        const activeUsers = userData.filter(user => user.last_login && new Date(user.last_login) > oneWeekAgo).length || Math.floor(totalUsers * 0.7);\n        const newUsers = userData.filter(user => user.created_at && new Date(user.created_at) > oneWeekAgo).length || Math.floor(totalUsers * 0.2);\n        const pendingUsers = userData.filter(user => user.status === 'pending').length || Math.floor(totalUsers * 0.1);\n        setStats({\n          totalUsers,\n          activeUsers,\n          newUsers,\n          pendingUsers\n        });\n      } else {\n        setError(response.data.message || 'Failed to fetch users');\n      }\n    } catch (err) {\n      setError('Failed to fetch users. Please check your network connection and try again.');\n      console.error('Error fetching users:', err);\n    }\n  };\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch teams');\n    }\n  };\n  const getTeamLogo = teamName => {\n    const team = teams.find(team => team.name === teamName);\n    return team ? `${API_BASE_URL}/${team.logo}` : null;\n  };\n  const getDefaultAvatar = () => {\n    return \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23e5e7eb'/%3E%3Cpath d='M20 20c3.3 0 6-2.7 6-6s-2.7-6-6-6-6 2.7-6 6 2.7 6 6 6zm0 3c-4 0-12 2-12 6v3h24v-3c0-4-8-6-12-6z' fill='%23374151'/%3E%3C/svg%3E\";\n  };\n  const handleEdit = user => {\n    setEditingUserId(user.user_id);\n    setEditingUser(user);\n  };\n  const handleUpdate = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.put(`${API_BASE_URL}/handlers/user_management.php?id=${editingUserId}`, editingUser);\n      if (response.data.success) {\n        setSuccess('User updated successfully!');\n        fetchUsers();\n        setEditingUserId(null);\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update user');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      setError('Failed to update user');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setEditingUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleNewUserInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleAddUser = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/add_user.php`, newUser);\n      if (response.data.success) {\n        setSuccess('User added successfully!');\n        fetchUsers();\n        setShowAddUserModal(false);\n        setNewUser({\n          username: '',\n          full_name: '',\n          email: '',\n          password: '',\n          favorite_team: '',\n          balance: 0\n        });\n        // Clear success message after 3 seconds\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to add user');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      setError('Failed to add user');\n      setTimeout(() => setError(''), 3000);\n    }\n  };\n  const handleViewUserDetails = async user => {\n    setLoadingUserDetails(true);\n    setShowUserDetailsModal(true);\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php?id=${user.user_id}`);\n      if (response.data.success) {\n        setSelectedUserDetails(response.data.data);\n      } else {\n        setError(response.data.message || 'Failed to fetch user details');\n        setShowUserDetailsModal(false);\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      setError('Failed to fetch user details');\n      setShowUserDetailsModal(false);\n      setTimeout(() => setError(''), 3000);\n      console.error('Error fetching user details:', err);\n    } finally {\n      setLoadingUserDetails(false);\n    }\n  };\n  const handleSuspendUser = async user => {\n    if (window.confirm(`Are you sure you want to suspend user \"${user.username}\"? This will prevent them from accessing their account.`)) {\n      try {\n        console.log('Suspending user:', user.user_id); // Debug log\n        const response = await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`, {\n          user_id: user.user_id,\n          action: 'suspend'\n        });\n        console.log('Suspend response:', response.data); // Debug log\n        if (response.data.success) {\n          setSuccess(`User \"${user.username}\" suspended successfully!`);\n          fetchUsers();\n          setTimeout(() => setSuccess(''), 3000);\n        } else {\n          setError(response.data.message || 'Failed to suspend user');\n          setTimeout(() => setError(''), 3000);\n        }\n      } catch (err) {\n        var _err$response, _err$response$data;\n        console.error('Suspend error:', err); // Debug log\n        setError('Failed to suspend user: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n        setTimeout(() => setError(''), 3000);\n      }\n    }\n  };\n  const handleBanUser = async user => {\n    if (window.confirm(`Are you sure you want to ban user \"${user.username}\"? This action cannot be undone and will permanently prevent them from accessing their account.`)) {\n      try {\n        console.log('Banning user:', user.user_id); // Debug log\n        const response = await axios.post(`${API_BASE_URL}/handlers/ban_user.php`, {\n          user_id: user.user_id,\n          action: 'ban'\n        });\n        console.log('Ban response:', response.data); // Debug log\n        if (response.data.success) {\n          setSuccess(`User \"${user.username}\" banned successfully!`);\n          fetchUsers();\n          setTimeout(() => setSuccess(''), 3000);\n        } else {\n          setError(response.data.message || 'Failed to ban user');\n          setTimeout(() => setError(''), 3000);\n        }\n      } catch (err) {\n        var _err$response2, _err$response2$data;\n        console.error('Ban error:', err); // Debug log\n        setError('Failed to ban user: ' + (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || err.message));\n        setTimeout(() => setError(''), 3000);\n      }\n    }\n  };\n\n  // Search functionality\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1); // Reset to first page when searching\n  };\n\n  // Filter users based on search term\n  const filteredUsers = users.filter(user => user.username.toLowerCase().includes(searchTerm.toLowerCase()) || user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase()));\n\n  // Pagination\n  const indexOfLastUser = currentPage * usersPerPage;\n  const indexOfFirstUser = indexOfLastUser - usersPerPage;\n  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);\n  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Generate page numbers\n  const pageNumbers = [];\n  for (let i = 1; i <= totalPages; i++) {\n    pageNumbers.push(i);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 bg-gray-50 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800\",\n        children: \"User Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Manage all users in the system\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-blue-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUsers, {\n            className: \"text-blue-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Total Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-green-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserCheck, {\n            className: \"text-green-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Active Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.activeUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-yellow-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserPlus, {\n            className: \"text-yellow-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"New Users (7d)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.newUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-purple-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserClock, {\n            className: \"text-purple-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Pending Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.pendingUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm p-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row md:items-center md:justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-white bg-blue-600 px-4 py-2 rounded mb-4 md:mb-0\",\n          children: \"User List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddUserModal(true),\n            className: \"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n            children: [/*#__PURE__*/_jsxDEV(FaUserPlus, {\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 29\n            }, this), \"Add User\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative w-full md:w-64\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n              children: /*#__PURE__*/_jsxDEV(FaSearch, {\n                className: \"h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n              placeholder: \"Search users...\",\n              value: searchTerm,\n              onChange: handleSearch\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"user-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-blue-600\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"#\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-small\",\n                children: \"Avatar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-medium\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-medium\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-small\",\n                children: \"Favorite Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-very-small\",\n                children: \"Balance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-actions\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: currentUsers.map((user, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: indexOfFirstUser + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap user-table-hide-small\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(user.favorite_team) || getDefaultAvatar(),\n                    alt: user.favorite_team || 'Default Avatar',\n                    className: \"w-10 h-10 rounded-full object-contain border border-gray-200\",\n                    onError: e => {\n                      e.target.src = getDefaultAvatar();\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.status === 'banned' ? 'bg-red-100 text-red-800' : user.status === 'suspended' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`,\n                  children: user.status === 'banned' ? 'BANNED' : user.status === 'suspended' ? 'SUSPENDED' : 'ACTIVE'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap user-table-hide-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: user.full_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap user-table-hide-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap user-table-hide-small\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [user.favorite_team && getTeamLogo(user.favorite_team) && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(user.favorite_team),\n                    alt: user.favorite_team,\n                    className: \"w-5 h-5 rounded-full object-contain\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: user.favorite_team || 'No team selected'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap user-table-hide-very-small\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: [user.balance, \" FC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium user-table-actions\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleViewUserDetails(user),\n                    className: \"text-green-600 hover:text-green-900 p-1\",\n                    title: \"View Details\",\n                    children: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(user),\n                    className: \"text-blue-600 hover:text-blue-900 p-1\",\n                    title: \"Edit User\",\n                    children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleSuspendUser(user),\n                    className: \"text-yellow-600 hover:text-yellow-900 p-1\",\n                    title: \"Suspend User\",\n                    children: /*#__PURE__*/_jsxDEV(FaUserSlash, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBanUser(user),\n                    className: \"text-red-600 hover:text-red-900 p-1\",\n                    title: \"Ban User\",\n                    children: /*#__PURE__*/_jsxDEV(FaBan, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 37\n              }, this)]\n            }, user.user_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 17\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-700\",\n              children: [\"Showing \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: indexOfFirstUser + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 45\n              }, this), \" to\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: indexOfLastUser > filteredUsers.length ? filteredUsers.length : indexOfLastUser\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 37\n              }, this), ' ', \"of \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: filteredUsers.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 40\n              }, this), \" results\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n              \"aria-label\": \"Pagination\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => paginate(currentPage > 1 ? currentPage - 1 : 1),\n                disabled: currentPage === 1,\n                className: `relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Previous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  \"aria-hidden\": \"true\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 37\n              }, this), pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => paginate(number),\n                className: `relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${currentPage === number ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'text-gray-500 hover:bg-gray-50'}`,\n                children: number\n              }, number, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 41\n              }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => paginate(currentPage < totalPages ? currentPage + 1 : totalPages),\n                disabled: currentPage === totalPages,\n                className: `relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Next\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  \"aria-hidden\": \"true\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 13\n    }, this), editingUserId && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center p-4 border-b\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-800\",\n            children: \"Edit User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\",\n            onClick: () => setEditingUserId(null),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleUpdate,\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"username\",\n                value: editingUser.username,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"full_name\",\n                value: editingUser.full_name,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: editingUser.email,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Favorite Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"favorite_team\",\n                value: editingUser.favorite_team,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Favorite Team\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 41\n                }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: team.name,\n                  children: team.name\n                }, team.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 45\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Balance (FanCoins)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"balance\",\n                value: editingUser.balance,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setEditingUserId(null),\n              className: \"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: \"Update User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 17\n    }, this), showAddUserModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center p-4 border-b\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-800\",\n            children: \"Add New User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\",\n            onClick: () => setShowAddUserModal(false),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleAddUser,\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"username\",\n                value: newUser.username,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"full_name\",\n                value: newUser.full_name,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: newUser.email,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"password\",\n                value: newUser.password,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Favorite Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"favorite_team\",\n                value: newUser.favorite_team,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Favorite Team\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 41\n                }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: team.name,\n                  children: team.name\n                }, team.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 45\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Initial Balance (FanCoins)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"balance\",\n                value: newUser.balance,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                min: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowAddUserModal(false),\n              className: \"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n              children: \"Add User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 17\n    }, this), showUserDetailsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sports-card-modal w-full max-w-4xl\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sports-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"absolute top-4 right-4 text-white hover:text-gray-200 text-2xl focus:outline-none z-10\",\n            onClick: () => {\n              setShowUserDetailsModal(false);\n              setSelectedUserDetails(null);\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 29\n          }, this), loadingUserDetails ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-spinner mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"Loading player stats...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 33\n          }, this) : selectedUserDetails ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sports-card-avatar\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: getTeamLogo(selectedUserDetails.favorite_team) || getDefaultAvatar(),\n                alt: selectedUserDetails.favorite_team || 'Player Avatar',\n                onError: e => {\n                  e.target.src = getDefaultAvatar();\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sports-card-name\",\n              children: selectedUserDetails.full_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg opacity-90 mb-2\",\n              children: [\"@\", selectedUserDetails.username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `sports-card-status ${selectedUserDetails.status === 'banned' ? 'bg-red-500' : selectedUserDetails.status === 'suspended' ? 'bg-yellow-500' : 'bg-green-500'}`,\n              children: [((_selectedUserDetails$ = selectedUserDetails.status) === null || _selectedUserDetails$ === void 0 ? void 0 : _selectedUserDetails$.toUpperCase()) || 'ACTIVE', \" PLAYER\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sports-card-body\",\n          children: !loadingUserDetails && selectedUserDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"performance-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"performance-stat\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"performance-number text-blue-600\",\n                  children: selectedUserDetails.total_bets || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"performance-label\",\n                  children: \"Total Bets\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 818,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"performance-stat\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"performance-number text-green-600\",\n                  children: selectedUserDetails.wins || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"performance-label\",\n                  children: \"Wins\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"performance-stat\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"performance-number text-yellow-600\",\n                  children: selectedUserDetails.draws || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"performance-label\",\n                  children: \"Draws\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"performance-stat\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"performance-number text-red-600\",\n                  children: selectedUserDetails.losses || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"performance-label\",\n                  children: \"Losses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-info-summary\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-info-label\",\n                  children: \"Player ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-info-value\",\n                  children: [\"#\", selectedUserDetails.user_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-info-label\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-info-value\",\n                  children: selectedUserDetails.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-info-label\",\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-info-value capitalize\",\n                  children: selectedUserDetails.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-info-label\",\n                  children: \"Favorite Team\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 849,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-info-value\",\n                  children: selectedUserDetails.favorite_team || 'None'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 850,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"compact-stats-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"compact-stat-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"compact-stat-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-title\",\n                    children: \"Financial & Points\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 859,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(FaCoins, {\n                    className: \"compact-stat-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"compact-stats-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Balance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 864,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value positive\",\n                      children: [selectedUserDetails.balance, \" FC\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Current Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.points || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Total Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 872,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.total_points || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Leagues\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 876,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.league_memberships || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 877,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 875,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"compact-stat-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"compact-stat-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-title\",\n                    children: \"Performance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(FaTrophy, {\n                    className: \"compact-stat-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"compact-stats-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Win Rate\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 890,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.total_bets > 0 ? `${Math.round(selectedUserDetails.wins / selectedUserDetails.total_bets * 100)}%` : '0%'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 891,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 889,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Current Streak\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 899,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.current_streak || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 900,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Best Streak\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 903,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.highest_streak || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 904,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Bets Created\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 907,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.total_bets_created || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 908,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"compact-stat-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"compact-stat-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-title\",\n                    children: \"Activity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 916,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                    className: \"compact-stat-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 917,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"compact-stats-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Joined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: new Date(selectedUserDetails.created_at).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 922,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Last Active\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 925,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.last_active ? new Date(selectedUserDetails.last_active).toLocaleDateString() : 'Never'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 926,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 924,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Last Bet\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.last_bet_date ? new Date(selectedUserDetails.last_bet_date).toLocaleDateString() : 'Never'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 935,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compact-stat-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-label\",\n                      children: \"Bets Joined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 943,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"compact-stat-value\",\n                      children: selectedUserDetails.total_bets_joined || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 944,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center p-6 bg-gray-50 border-t border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowUserDetailsModal(false);\n              setSelectedUserDetails(null);\n            },\n            className: \"px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium shadow-lg\",\n            style: {\n              backgroundColor: '#166534'\n            },\n            children: \"Close Player Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 955,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 768,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 9\n  }, this);\n}\n_s(UserManagement, \"xIggAr050zbIhlFxMI+5+/yly8w=\", false, function () {\n  return [useNavigate];\n});\n_c = UserManagement;\nexport default UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "FaUsers", "FaUserPlus", "FaUserCheck", "FaUserClock", "FaEdit", "FaSearch", "FaEye", "FaUserSlash", "FaBan", "FaTrophy", "FaCoins", "FaChartLine", "FaCalendarAlt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "UserManagement", "_s", "_selectedUserDetails$", "navigate", "users", "setUsers", "teams", "setTeams", "error", "setError", "success", "setSuccess", "editingUserId", "setEditingUserId", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "usersPerPage", "stats", "setStats", "totalUsers", "activeUsers", "newUsers", "pendingUsers", "editingUser", "setEditingUser", "username", "full_name", "email", "favorite_team", "balance", "showAddUserModal", "setShowAddUserModal", "showUserDetailsModal", "setShowUserDetailsModal", "selectedUserDetails", "setSelectedUserDetails", "loadingUserDetails", "setLoadingUserDetails", "newUser", "setNewUser", "password", "fetchUsers", "fetchTeams", "response", "get", "data", "userData", "now", "Date", "oneWeekAgo", "getTime", "length", "filter", "user", "last_login", "Math", "floor", "created_at", "status", "message", "err", "console", "getTeamLogo", "teamName", "team", "find", "name", "logo", "getDefaultAvatar", "handleEdit", "user_id", "handleUpdate", "e", "preventDefault", "put", "setTimeout", "handleInputChange", "value", "target", "prev", "handleNewUserInputChange", "handleAddUser", "post", "handleViewUserDetails", "handleSuspendUser", "window", "confirm", "log", "action", "_err$response", "_err$response$data", "handleBanUser", "_err$response2", "_err$response2$data", "handleSearch", "filteredUsers", "toLowerCase", "includes", "indexOfLastUser", "indexOfFirstUser", "currentUsers", "slice", "totalPages", "ceil", "paginate", "pageNumber", "pageNumbers", "i", "push", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "onClick", "type", "placeholder", "onChange", "scope", "map", "index", "src", "alt", "onError", "title", "disabled", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "number", "onSubmit", "required", "id", "min", "toUpperCase", "total_bets", "wins", "draws", "losses", "points", "total_points", "league_memberships", "round", "current_streak", "highest_streak", "total_bets_created", "toLocaleDateString", "last_active", "last_bet_date", "total_bets_joined", "style", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { FaUsers, FaUserPlus, FaUserCheck, FaUserClock, FaEdit, FaSearch, FaEye, FaUserSlash, FaBan, FaTrophy, FaCoins, FaChartLine, FaCalendarAlt } from 'react-icons/fa';\r\nimport './UserManagement.css';\r\n// import CustomModal from '../components/CustomModal';\r\n\r\nconst API_BASE_URL = '/backend';\r\n\r\nfunction UserManagement() {\r\n    const navigate = useNavigate();\r\n    const [users, setUsers] = useState([]);\r\n    const [teams, setTeams] = useState([]);\r\n    const [error, setError] = useState('');\r\n    const [success, setSuccess] = useState('');\r\n    const [editingUserId, setEditingUserId] = useState(null);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [usersPerPage] = useState(10);\r\n    const [stats, setStats] = useState({\r\n        totalUsers: 0,\r\n        activeUsers: 0,\r\n        newUsers: 0,\r\n        pendingUsers: 0\r\n    });\r\n    const [editingUser, setEditingUser] = useState({\r\n        username: '',\r\n        full_name: '',\r\n        email: '',\r\n        favorite_team: '',\r\n        balance: 0\r\n    });\r\n    const [showAddUserModal, setShowAddUserModal] = useState(false);\r\n    const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);\r\n    const [selectedUserDetails, setSelectedUserDetails] = useState(null);\r\n    const [loadingUserDetails, setLoadingUserDetails] = useState(false);\r\n    const [newUser, setNewUser] = useState({\r\n        username: '',\r\n        full_name: '',\r\n        email: '',\r\n        password: '',\r\n        favorite_team: '',\r\n        balance: 0\r\n    });\r\n\r\n    // Modal states - temporarily disabled\r\n    // const [modalState, setModalState] = useState({\r\n    //     isOpen: false,\r\n    //     type: 'confirm',\r\n    //     title: '',\r\n    //     message: '',\r\n    //     onConfirm: null,\r\n    //     confirmText: 'Confirm',\r\n    //     confirmButtonColor: 'blue'\r\n    // });\r\n\r\n    useEffect(() => {\r\n        fetchUsers();\r\n        fetchTeams();\r\n    }, []);\r\n\r\n    const fetchUsers = async () => {\r\n        try {\r\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php`);\r\n            if (response.data.success) {\r\n                const userData = response.data.data || [];\r\n                setUsers(userData);\r\n\r\n                // Calculate stats\r\n                const now = new Date();\r\n                const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\r\n\r\n                // For demo purposes, we'll simulate some stats\r\n                const totalUsers = userData.length;\r\n                const activeUsers = userData.filter(user => user.last_login && new Date(user.last_login) > oneWeekAgo).length || Math.floor(totalUsers * 0.7);\r\n                const newUsers = userData.filter(user => user.created_at && new Date(user.created_at) > oneWeekAgo).length || Math.floor(totalUsers * 0.2);\r\n                const pendingUsers = userData.filter(user => user.status === 'pending').length || Math.floor(totalUsers * 0.1);\r\n\r\n                setStats({\r\n                    totalUsers,\r\n                    activeUsers,\r\n                    newUsers,\r\n                    pendingUsers\r\n                });\r\n            } else {\r\n                setError(response.data.message || 'Failed to fetch users');\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to fetch users. Please check your network connection and try again.');\r\n            console.error('Error fetching users:', err);\r\n        }\r\n    };\r\n\r\n    const fetchTeams = async () => {\r\n        try {\r\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\r\n            setTeams(response.data.data || []);\r\n        } catch (err) {\r\n            setError('Failed to fetch teams');\r\n        }\r\n    };\r\n\r\n    const getTeamLogo = (teamName) => {\r\n        const team = teams.find(team => team.name === teamName);\r\n        return team ? `${API_BASE_URL}/${team.logo}` : null;\r\n    };\r\n\r\n    const getDefaultAvatar = () => {\r\n        return \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23e5e7eb'/%3E%3Cpath d='M20 20c3.3 0 6-2.7 6-6s-2.7-6-6-6-6 2.7-6 6 2.7 6 6 6zm0 3c-4 0-12 2-12 6v3h24v-3c0-4-8-6-12-6z' fill='%23374151'/%3E%3C/svg%3E\";\r\n    };\r\n\r\n    const handleEdit = (user) => {\r\n        setEditingUserId(user.user_id);\r\n        setEditingUser(user);\r\n    };\r\n\r\n    const handleUpdate = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.put(`${API_BASE_URL}/handlers/user_management.php?id=${editingUserId}`, editingUser);\r\n            if (response.data.success) {\r\n                setSuccess('User updated successfully!');\r\n                fetchUsers();\r\n                setEditingUserId(null);\r\n                setTimeout(() => setSuccess(''), 3000);\r\n            } else {\r\n                setError(response.data.message || 'Failed to update user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to update user');\r\n        }\r\n    };\r\n\r\n    const handleInputChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setEditingUser(prev => ({ ...prev, [name]: value }));\r\n    };\r\n\r\n    const handleNewUserInputChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setNewUser(prev => ({ ...prev, [name]: value }));\r\n    };\r\n\r\n    const handleAddUser = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.post(`${API_BASE_URL}/handlers/add_user.php`, newUser);\r\n            if (response.data.success) {\r\n                setSuccess('User added successfully!');\r\n                fetchUsers();\r\n                setShowAddUserModal(false);\r\n                setNewUser({\r\n                    username: '',\r\n                    full_name: '',\r\n                    email: '',\r\n                    password: '',\r\n                    favorite_team: '',\r\n                    balance: 0\r\n                });\r\n                // Clear success message after 3 seconds\r\n                setTimeout(() => setSuccess(''), 3000);\r\n            } else {\r\n                setError(response.data.message || 'Failed to add user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to add user');\r\n            setTimeout(() => setError(''), 3000);\r\n        }\r\n    };\r\n\r\n    const handleViewUserDetails = async (user) => {\r\n        setLoadingUserDetails(true);\r\n        setShowUserDetailsModal(true);\r\n        try {\r\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php?id=${user.user_id}`);\r\n            if (response.data.success) {\r\n                setSelectedUserDetails(response.data.data);\r\n            } else {\r\n                setError(response.data.message || 'Failed to fetch user details');\r\n                setShowUserDetailsModal(false);\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to fetch user details');\r\n            setShowUserDetailsModal(false);\r\n            setTimeout(() => setError(''), 3000);\r\n            console.error('Error fetching user details:', err);\r\n        } finally {\r\n            setLoadingUserDetails(false);\r\n        }\r\n    };\r\n\r\n    const handleSuspendUser = async (user) => {\r\n        if (window.confirm(`Are you sure you want to suspend user \"${user.username}\"? This will prevent them from accessing their account.`)) {\r\n            try {\r\n                console.log('Suspending user:', user.user_id); // Debug log\r\n                const response = await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`, {\r\n                    user_id: user.user_id,\r\n                    action: 'suspend'\r\n                });\r\n                console.log('Suspend response:', response.data); // Debug log\r\n                if (response.data.success) {\r\n                    setSuccess(`User \"${user.username}\" suspended successfully!`);\r\n                    fetchUsers();\r\n                    setTimeout(() => setSuccess(''), 3000);\r\n                } else {\r\n                    setError(response.data.message || 'Failed to suspend user');\r\n                    setTimeout(() => setError(''), 3000);\r\n                }\r\n            } catch (err) {\r\n                console.error('Suspend error:', err); // Debug log\r\n                setError('Failed to suspend user: ' + (err.response?.data?.message || err.message));\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        }\r\n    };\r\n\r\n    const handleBanUser = async (user) => {\r\n        if (window.confirm(`Are you sure you want to ban user \"${user.username}\"? This action cannot be undone and will permanently prevent them from accessing their account.`)) {\r\n            try {\r\n                console.log('Banning user:', user.user_id); // Debug log\r\n                const response = await axios.post(`${API_BASE_URL}/handlers/ban_user.php`, {\r\n                    user_id: user.user_id,\r\n                    action: 'ban'\r\n                });\r\n                console.log('Ban response:', response.data); // Debug log\r\n                if (response.data.success) {\r\n                    setSuccess(`User \"${user.username}\" banned successfully!`);\r\n                    fetchUsers();\r\n                    setTimeout(() => setSuccess(''), 3000);\r\n                } else {\r\n                    setError(response.data.message || 'Failed to ban user');\r\n                    setTimeout(() => setError(''), 3000);\r\n                }\r\n            } catch (err) {\r\n                console.error('Ban error:', err); // Debug log\r\n                setError('Failed to ban user: ' + (err.response?.data?.message || err.message));\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        }\r\n    };\r\n\r\n    // Search functionality\r\n    const handleSearch = (e) => {\r\n        setSearchTerm(e.target.value);\r\n        setCurrentPage(1); // Reset to first page when searching\r\n    };\r\n\r\n    // Filter users based on search term\r\n    const filteredUsers = users.filter(user =>\r\n        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        user.email.toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n\r\n    // Pagination\r\n    const indexOfLastUser = currentPage * usersPerPage;\r\n    const indexOfFirstUser = indexOfLastUser - usersPerPage;\r\n    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);\r\n    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);\r\n\r\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n    // Generate page numbers\r\n    const pageNumbers = [];\r\n    for (let i = 1; i <= totalPages; i++) {\r\n        pageNumbers.push(i);\r\n    }\r\n\r\n    return (\r\n        <div className=\"p-6 bg-gray-50 min-h-screen\">\r\n            {/* Page Header */}\r\n            <div className=\"mb-8\">\r\n                <h1 className=\"text-2xl font-bold text-gray-800\">User Management</h1>\r\n                <p className=\"text-gray-600\">Manage all users in the system</p>\r\n            </div>\r\n\r\n            {/* Notification Messages */}\r\n            {error && (\r\n                <div className=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n                    <span className=\"block sm:inline\">{error}</span>\r\n                </div>\r\n            )}\r\n            {success && (\r\n                <div className=\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n                    <span className=\"block sm:inline\">{success}</span>\r\n                </div>\r\n            )}\r\n\r\n            {/* Stats Cards */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n                {/* Total Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-blue-100 p-3 mr-4\">\r\n                        <FaUsers className=\"text-blue-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Total Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.totalUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Active Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-green-100 p-3 mr-4\">\r\n                        <FaUserCheck className=\"text-green-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Active Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.activeUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* New Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-yellow-100 p-3 mr-4\">\r\n                        <FaUserPlus className=\"text-yellow-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">New Users (7d)</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.newUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Pending Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-purple-100 p-3 mr-4\">\r\n                        <FaUserClock className=\"text-purple-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Pending Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.pendingUsers}</h3>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Search and Filter */}\r\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-8\">\r\n                <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-4\">\r\n                    <h2 className=\"text-lg font-semibold text-white bg-blue-600 px-4 py-2 rounded mb-4 md:mb-0\">User List</h2>\r\n                    <div className=\"flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4\">\r\n                        <button\r\n                            onClick={() => setShowAddUserModal(true)}\r\n                            className=\"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\r\n                        >\r\n                            <FaUserPlus className=\"mr-2\" />\r\n                            Add User\r\n                        </button>\r\n                        <div className=\"relative w-full md:w-64\">\r\n                            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                                <FaSearch className=\"h-4 w-4 text-gray-400\" />\r\n                            </div>\r\n                            <input\r\n                                type=\"text\"\r\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\r\n                                placeholder=\"Search users...\"\r\n                                value={searchTerm}\r\n                                onChange={handleSearch}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Users Table */}\r\n                <div className=\"user-table-container\">\r\n                    <table className=\"user-table\">\r\n                        <thead className=\"bg-blue-600\">\r\n                            <tr>\r\n                                <th scope=\"col\" className=\"px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    #\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-small\">\r\n                                    Avatar\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Username\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Status\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-medium\">\r\n                                    Full Name\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-medium\">\r\n                                    Email\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-small\">\r\n                                    Favorite Team\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-very-small\">\r\n                                    Balance\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-actions\">\r\n                                    Actions\r\n                                </th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                            {currentUsers.map((user, index) => (\r\n                                <tr key={user.user_id} className=\"hover:bg-gray-50\">\r\n                                    <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                        {indexOfFirstUser + index + 1}\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap user-table-hide-small\">\r\n                                        <div className=\"flex items-center\">\r\n                                            <img\r\n                                                src={getTeamLogo(user.favorite_team) || getDefaultAvatar()}\r\n                                                alt={user.favorite_team || 'Default Avatar'}\r\n                                                className=\"w-10 h-10 rounded-full object-contain border border-gray-200\"\r\n                                                onError={(e) => {\r\n                                                    e.target.src = getDefaultAvatar();\r\n                                                }}\r\n                                            />\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm font-medium text-gray-900\">{user.username}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\r\n                                            user.status === 'banned' ? 'bg-red-100 text-red-800' :\r\n                                            user.status === 'suspended' ? 'bg-yellow-100 text-yellow-800' :\r\n                                            'bg-green-100 text-green-800'\r\n                                        }`}>\r\n                                            {user.status === 'banned' ? 'BANNED' :\r\n                                             user.status === 'suspended' ? 'SUSPENDED' :\r\n                                             'ACTIVE'}\r\n                                        </span>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap user-table-hide-medium\">\r\n                                        <div className=\"text-sm text-gray-500\">{user.full_name}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap user-table-hide-medium\">\r\n                                        <div className=\"text-sm text-gray-500\">{user.email}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap user-table-hide-small\">\r\n                                        <div className=\"flex items-center space-x-2\">\r\n                                            {user.favorite_team && getTeamLogo(user.favorite_team) && (\r\n                                                <img\r\n                                                    src={getTeamLogo(user.favorite_team)}\r\n                                                    alt={user.favorite_team}\r\n                                                    className=\"w-5 h-5 rounded-full object-contain\"\r\n                                                />\r\n                                            )}\r\n                                            <div className=\"text-sm text-gray-500\">{user.favorite_team || 'No team selected'}</div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap user-table-hide-very-small\">\r\n                                        <div className=\"text-sm font-medium text-gray-900\">{user.balance} FC</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium user-table-actions\">\r\n                                        <div className=\"flex space-x-1\">\r\n                                            <button\r\n                                                onClick={() => handleViewUserDetails(user)}\r\n                                                className=\"text-green-600 hover:text-green-900 p-1\"\r\n                                                title=\"View Details\"\r\n                                            >\r\n                                                <FaEye />\r\n                                            </button>\r\n                                            <button\r\n                                                onClick={() => handleEdit(user)}\r\n                                                className=\"text-blue-600 hover:text-blue-900 p-1\"\r\n                                                title=\"Edit User\"\r\n                                            >\r\n                                                <FaEdit />\r\n                                            </button>\r\n                                            <button\r\n                                                onClick={() => handleSuspendUser(user)}\r\n                                                className=\"text-yellow-600 hover:text-yellow-900 p-1\"\r\n                                                title=\"Suspend User\"\r\n                                            >\r\n                                                <FaUserSlash />\r\n                                            </button>\r\n                                            <button\r\n                                                onClick={() => handleBanUser(user)}\r\n                                                className=\"text-red-600 hover:text-red-900 p-1\"\r\n                                                title=\"Ban User\"\r\n                                            >\r\n                                                <FaBan />\r\n                                            </button>\r\n                                        </div>\r\n                                    </td>\r\n                                </tr>\r\n                            ))}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n\r\n                {/* Pagination */}\r\n                {totalPages > 1 && (\r\n                    <div className=\"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4\">\r\n                        <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\r\n                            <div>\r\n                                <p className=\"text-sm text-gray-700\">\r\n                                    Showing <span className=\"font-medium\">{indexOfFirstUser + 1}</span> to{' '}\r\n                                    <span className=\"font-medium\">\r\n                                        {indexOfLastUser > filteredUsers.length ? filteredUsers.length : indexOfLastUser}\r\n                                    </span>{' '}\r\n                                    of <span className=\"font-medium\">{filteredUsers.length}</span> results\r\n                                </p>\r\n                            </div>\r\n                            <div>\r\n                                <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\r\n                                    <button\r\n                                        onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}\r\n                                        disabled={currentPage === 1}\r\n                                        className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${\r\n                                            currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'\r\n                                        }`}\r\n                                    >\r\n                                        <span className=\"sr-only\">Previous</span>\r\n                                        <svg className=\"h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\r\n                                            <path fillRule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                                        </svg>\r\n                                    </button>\r\n\r\n                                    {pageNumbers.map(number => (\r\n                                        <button\r\n                                            key={number}\r\n                                            onClick={() => paginate(number)}\r\n                                            className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${\r\n                                                currentPage === number\r\n                                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\r\n                                                    : 'text-gray-500 hover:bg-gray-50'\r\n                                            }`}\r\n                                        >\r\n                                            {number}\r\n                                        </button>\r\n                                    ))}\r\n\r\n                                    <button\r\n                                        onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)}\r\n                                        disabled={currentPage === totalPages}\r\n                                        className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${\r\n                                            currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'\r\n                                        }`}\r\n                                    >\r\n                                        <span className=\"sr-only\">Next</span>\r\n                                        <svg className=\"h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\r\n                                            <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\r\n                                        </svg>\r\n                                    </button>\r\n                                </nav>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Edit User Modal */}\r\n            {editingUserId && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                    <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\r\n                        <div className=\"flex justify-between items-center p-4 border-b\">\r\n                            <h3 className=\"text-lg font-semibold text-gray-800\">Edit User</h3>\r\n                            <button\r\n                                className=\"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\"\r\n                                onClick={() => setEditingUserId(null)}\r\n                            >\r\n                                ×\r\n                            </button>\r\n                        </div>\r\n\r\n                        <form onSubmit={handleUpdate} className=\"p-6\">\r\n                            <div className=\"space-y-4\">\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"username\"\r\n                                        value={editingUser.username}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Full Name</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"full_name\"\r\n                                        value={editingUser.full_name}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                                    <input\r\n                                        type=\"email\"\r\n                                        name=\"email\"\r\n                                        value={editingUser.email}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Favorite Team</label>\r\n                                    <select\r\n                                        name=\"favorite_team\"\r\n                                        value={editingUser.favorite_team}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    >\r\n                                        <option value=\"\">Select Favorite Team</option>\r\n                                        {teams.map(team => (\r\n                                            <option key={team.id} value={team.name}>{team.name}</option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Balance (FanCoins)</label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        name=\"balance\"\r\n                                        value={editingUser.balance}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"mt-6 flex justify-end space-x-3\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={() => setEditingUserId(null)}\r\n                                    className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                                >\r\n                                    Update User\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {/* Add User Modal */}\r\n            {showAddUserModal && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                    <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\r\n                        <div className=\"flex justify-between items-center p-4 border-b\">\r\n                            <h3 className=\"text-lg font-semibold text-gray-800\">Add New User</h3>\r\n                            <button\r\n                                className=\"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\"\r\n                                onClick={() => setShowAddUserModal(false)}\r\n                            >\r\n                                ×\r\n                            </button>\r\n                        </div>\r\n\r\n                        <form onSubmit={handleAddUser} className=\"p-6\">\r\n                            <div className=\"space-y-4\">\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"username\"\r\n                                        value={newUser.username}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Full Name</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"full_name\"\r\n                                        value={newUser.full_name}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                                    <input\r\n                                        type=\"email\"\r\n                                        name=\"email\"\r\n                                        value={newUser.email}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Password</label>\r\n                                    <input\r\n                                        type=\"password\"\r\n                                        name=\"password\"\r\n                                        value={newUser.password}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Favorite Team</label>\r\n                                    <select\r\n                                        name=\"favorite_team\"\r\n                                        value={newUser.favorite_team}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                    >\r\n                                        <option value=\"\">Select Favorite Team</option>\r\n                                        {teams.map(team => (\r\n                                            <option key={team.id} value={team.name}>{team.name}</option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Initial Balance (FanCoins)</label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        name=\"balance\"\r\n                                        value={newUser.balance}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        min=\"0\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"mt-6 flex justify-end space-x-3\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={() => setShowAddUserModal(false)}\r\n                                    className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n                                >\r\n                                    Add User\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {/* Sports Card User Details Modal */}\r\n            {showUserDetailsModal && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                    <div className=\"sports-card-modal w-full max-w-4xl\">\r\n                        {/* Sports Card Header */}\r\n                        <div className=\"sports-card-header\">\r\n                            <button\r\n                                className=\"absolute top-4 right-4 text-white hover:text-gray-200 text-2xl focus:outline-none z-10\"\r\n                                onClick={() => {\r\n                                    setShowUserDetailsModal(false);\r\n                                    setSelectedUserDetails(null);\r\n                                }}\r\n                            >\r\n                                ×\r\n                            </button>\r\n\r\n                            {loadingUserDetails ? (\r\n                                <div className=\"flex flex-col items-center\">\r\n                                    <div className=\"loading-spinner mb-4\"></div>\r\n                                    <span className=\"text-lg\">Loading player stats...</span>\r\n                                </div>\r\n                            ) : selectedUserDetails ? (\r\n                                <>\r\n                                    <div className=\"sports-card-avatar\">\r\n                                        <img\r\n                                            src={getTeamLogo(selectedUserDetails.favorite_team) || getDefaultAvatar()}\r\n                                            alt={selectedUserDetails.favorite_team || 'Player Avatar'}\r\n                                            onError={(e) => {\r\n                                                e.target.src = getDefaultAvatar();\r\n                                            }}\r\n                                        />\r\n                                    </div>\r\n                                    <div className=\"sports-card-name\">{selectedUserDetails.full_name}</div>\r\n                                    <div className=\"text-lg opacity-90 mb-2\">@{selectedUserDetails.username}</div>\r\n                                    <div className={`sports-card-status ${\r\n                                        selectedUserDetails.status === 'banned' ? 'bg-red-500' :\r\n                                        selectedUserDetails.status === 'suspended' ? 'bg-yellow-500' :\r\n                                        'bg-green-500'\r\n                                    }`}>\r\n                                        {selectedUserDetails.status?.toUpperCase() || 'ACTIVE'} PLAYER\r\n                                    </div>\r\n                                </>\r\n                            ) : null}\r\n                        </div>\r\n\r\n                        {/* Sports Card Body */}\r\n                        <div className=\"sports-card-body\">\r\n                            {!loadingUserDetails && selectedUserDetails && (\r\n                                <div className=\"space-y-4\">\r\n                                    {/* Performance Stats Grid */}\r\n                                    <div className=\"performance-grid\">\r\n                                        <div className=\"performance-stat\">\r\n                                            <div className=\"performance-number text-blue-600\">{selectedUserDetails.total_bets || 0}</div>\r\n                                            <div className=\"performance-label\">Total Bets</div>\r\n                                        </div>\r\n                                        <div className=\"performance-stat\">\r\n                                            <div className=\"performance-number text-green-600\">{selectedUserDetails.wins || 0}</div>\r\n                                            <div className=\"performance-label\">Wins</div>\r\n                                        </div>\r\n                                        <div className=\"performance-stat\">\r\n                                            <div className=\"performance-number text-yellow-600\">{selectedUserDetails.draws || 0}</div>\r\n                                            <div className=\"performance-label\">Draws</div>\r\n                                        </div>\r\n                                        <div className=\"performance-stat\">\r\n                                            <div className=\"performance-number text-red-600\">{selectedUserDetails.losses || 0}</div>\r\n                                            <div className=\"performance-label\">Losses</div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* User Information Summary */}\r\n                                    <div className=\"user-info-summary\">\r\n                                        <div className=\"user-info-row\">\r\n                                            <span className=\"user-info-label\">Player ID</span>\r\n                                            <span className=\"user-info-value\">#{selectedUserDetails.user_id}</span>\r\n                                        </div>\r\n                                        <div className=\"user-info-row\">\r\n                                            <span className=\"user-info-label\">Email</span>\r\n                                            <span className=\"user-info-value\">{selectedUserDetails.email}</span>\r\n                                        </div>\r\n                                        <div className=\"user-info-row\">\r\n                                            <span className=\"user-info-label\">Role</span>\r\n                                            <span className=\"user-info-value capitalize\">{selectedUserDetails.role}</span>\r\n                                        </div>\r\n                                        <div className=\"user-info-row\">\r\n                                            <span className=\"user-info-label\">Favorite Team</span>\r\n                                            <span className=\"user-info-value\">{selectedUserDetails.favorite_team || 'None'}</span>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Compact Stats Grid */}\r\n                                    <div className=\"compact-stats-grid\">\r\n                                        {/* Financial & Points */}\r\n                                        <div className=\"compact-stat-card\">\r\n                                            <div className=\"compact-stat-header\">\r\n                                                <div className=\"compact-stat-title\">Financial & Points</div>\r\n                                                <FaCoins className=\"compact-stat-icon\" />\r\n                                            </div>\r\n                                            <div className=\"compact-stats-row\">\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Balance</div>\r\n                                                    <div className=\"compact-stat-value positive\">{selectedUserDetails.balance} FC</div>\r\n                                                </div>\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Current Points</div>\r\n                                                    <div className=\"compact-stat-value\">{selectedUserDetails.points || 0}</div>\r\n                                                </div>\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Total Points</div>\r\n                                                    <div className=\"compact-stat-value\">{selectedUserDetails.total_points || 0}</div>\r\n                                                </div>\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Leagues</div>\r\n                                                    <div className=\"compact-stat-value\">{selectedUserDetails.league_memberships || 0}</div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* Performance & Streaks */}\r\n                                        <div className=\"compact-stat-card\">\r\n                                            <div className=\"compact-stat-header\">\r\n                                                <div className=\"compact-stat-title\">Performance</div>\r\n                                                <FaTrophy className=\"compact-stat-icon\" />\r\n                                            </div>\r\n                                            <div className=\"compact-stats-row\">\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Win Rate</div>\r\n                                                    <div className=\"compact-stat-value\">\r\n                                                        {selectedUserDetails.total_bets > 0\r\n                                                            ? `${Math.round((selectedUserDetails.wins / selectedUserDetails.total_bets) * 100)}%`\r\n                                                            : '0%'\r\n                                                        }\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Current Streak</div>\r\n                                                    <div className=\"compact-stat-value\">{selectedUserDetails.current_streak || 0}</div>\r\n                                                </div>\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Best Streak</div>\r\n                                                    <div className=\"compact-stat-value\">{selectedUserDetails.highest_streak || 0}</div>\r\n                                                </div>\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Bets Created</div>\r\n                                                    <div className=\"compact-stat-value\">{selectedUserDetails.total_bets_created || 0}</div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* Activity & Dates */}\r\n                                        <div className=\"compact-stat-card\">\r\n                                            <div className=\"compact-stat-header\">\r\n                                                <div className=\"compact-stat-title\">Activity</div>\r\n                                                <FaCalendarAlt className=\"compact-stat-icon\" />\r\n                                            </div>\r\n                                            <div className=\"compact-stats-row\">\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Joined</div>\r\n                                                    <div className=\"compact-stat-value\">{new Date(selectedUserDetails.created_at).toLocaleDateString()}</div>\r\n                                                </div>\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Last Active</div>\r\n                                                    <div className=\"compact-stat-value\">\r\n                                                        {selectedUserDetails.last_active ?\r\n                                                            new Date(selectedUserDetails.last_active).toLocaleDateString() :\r\n                                                            'Never'\r\n                                                        }\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Last Bet</div>\r\n                                                    <div className=\"compact-stat-value\">\r\n                                                        {selectedUserDetails.last_bet_date ?\r\n                                                            new Date(selectedUserDetails.last_bet_date).toLocaleDateString() :\r\n                                                            'Never'\r\n                                                        }\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div className=\"compact-stat-item\">\r\n                                                    <div className=\"compact-stat-label\">Bets Joined</div>\r\n                                                    <div className=\"compact-stat-value\">{selectedUserDetails.total_bets_joined || 0}</div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n\r\n                        {/* Modal Footer */}\r\n                        <div className=\"flex justify-center p-6 bg-gray-50 border-t border-gray-200\">\r\n                            <button\r\n                                onClick={() => {\r\n                                    setShowUserDetailsModal(false);\r\n                                    setSelectedUserDetails(null);\r\n                                }}\r\n                                className=\"px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium shadow-lg\"\r\n                                style={{ backgroundColor: '#166534' }}\r\n                            >\r\n                                Close Player Card\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {/* Custom Modal - Temporarily disabled */}\r\n            {/*\r\n            <CustomModal\r\n                isOpen={modalState.isOpen}\r\n                onClose={() => setModalState({ ...modalState, isOpen: false })}\r\n                onConfirm={modalState.onConfirm}\r\n                title={modalState.title}\r\n                message={modalState.message}\r\n                type={modalState.type}\r\n                confirmText={modalState.confirmText}\r\n                confirmButtonColor={modalState.confirmButtonColor}\r\n            />\r\n            */}\r\n        </div>\r\n    );\r\n}\r\nexport default UserManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,aAAa,QAAQ,gBAAgB;AAC1K,OAAO,sBAAsB;AAC7B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACtB,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC;IAC/B2C,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC;IAC3CiD,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC0D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAAC4D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC;IACnCiD,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTa,QAAQ,EAAE,EAAE;IACZZ,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE;EACb,CAAC,CAAC;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEApD,SAAS,CAAC,MAAM;IACZgE,UAAU,CAAC,CAAC;IACZC,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAME,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,GAAG,CAAC,GAAG/C,YAAY,+BAA+B,CAAC;MAChF,IAAI8C,QAAQ,CAACE,IAAI,CAACrC,OAAO,EAAE;QACvB,MAAMsC,QAAQ,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE;QACzC1C,QAAQ,CAAC2C,QAAQ,CAAC;;QAElB;QACA,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;;QAEpE;QACA,MAAM/B,UAAU,GAAG2B,QAAQ,CAACK,MAAM;QAClC,MAAM/B,WAAW,GAAG0B,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,IAAI,IAAIN,IAAI,CAACK,IAAI,CAACC,UAAU,CAAC,GAAGL,UAAU,CAAC,CAACE,MAAM,IAAII,IAAI,CAACC,KAAK,CAACrC,UAAU,GAAG,GAAG,CAAC;QAC7I,MAAME,QAAQ,GAAGyB,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACI,UAAU,IAAI,IAAIT,IAAI,CAACK,IAAI,CAACI,UAAU,CAAC,GAAGR,UAAU,CAAC,CAACE,MAAM,IAAII,IAAI,CAACC,KAAK,CAACrC,UAAU,GAAG,GAAG,CAAC;QAC1I,MAAMG,YAAY,GAAGwB,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACK,MAAM,KAAK,SAAS,CAAC,CAACP,MAAM,IAAII,IAAI,CAACC,KAAK,CAACrC,UAAU,GAAG,GAAG,CAAC;QAE9GD,QAAQ,CAAC;UACLC,UAAU;UACVC,WAAW;UACXC,QAAQ;UACRC;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACHf,QAAQ,CAACoC,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,uBAAuB,CAAC;MAC9D;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVrD,QAAQ,CAAC,4EAA4E,CAAC;MACtFsD,OAAO,CAACvD,KAAK,CAAC,uBAAuB,EAAEsD,GAAG,CAAC;IAC/C;EACJ,CAAC;EAED,MAAMlB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,GAAG,CAAC,GAAG/C,YAAY,+BAA+B,CAAC;MAChFQ,QAAQ,CAACsC,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACtC,CAAC,CAAC,OAAOe,GAAG,EAAE;MACVrD,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;EAED,MAAMuD,WAAW,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,IAAI,GAAG5D,KAAK,CAAC6D,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,IAAI,KAAKH,QAAQ,CAAC;IACvD,OAAOC,IAAI,GAAG,GAAGnE,YAAY,IAAImE,IAAI,CAACG,IAAI,EAAE,GAAG,IAAI;EACvD,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,OAAO,2SAA2S;EACtT,CAAC;EAED,MAAMC,UAAU,GAAIhB,IAAI,IAAK;IACzB1C,gBAAgB,CAAC0C,IAAI,CAACiB,OAAO,CAAC;IAC9B9C,cAAc,CAAC6B,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACA,MAAM9B,QAAQ,GAAG,MAAMhE,KAAK,CAAC+F,GAAG,CAAC,GAAG7E,YAAY,oCAAoCa,aAAa,EAAE,EAAEa,WAAW,CAAC;MACjH,IAAIoB,QAAQ,CAACE,IAAI,CAACrC,OAAO,EAAE;QACvBC,UAAU,CAAC,4BAA4B,CAAC;QACxCgC,UAAU,CAAC,CAAC;QACZ9B,gBAAgB,CAAC,IAAI,CAAC;QACtBgE,UAAU,CAAC,MAAMlE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACoC,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,uBAAuB,CAAC;QAC1DgB,UAAU,CAAC,MAAMpE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqD,GAAG,EAAE;MACVrD,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;EAED,MAAMqE,iBAAiB,GAAIJ,CAAC,IAAK;IAC7B,MAAM;MAAEN,IAAI;MAAEW;IAAM,CAAC,GAAGL,CAAC,CAACM,MAAM;IAChCtD,cAAc,CAACuD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACb,IAAI,GAAGW;IAAM,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,MAAMG,wBAAwB,GAAIR,CAAC,IAAK;IACpC,MAAM;MAAEN,IAAI;MAAEW;IAAM,CAAC,GAAGL,CAAC,CAACM,MAAM;IAChCvC,UAAU,CAACwC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACb,IAAI,GAAGW;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMI,aAAa,GAAG,MAAOT,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACA,MAAM9B,QAAQ,GAAG,MAAMhE,KAAK,CAACuG,IAAI,CAAC,GAAGrF,YAAY,wBAAwB,EAAEyC,OAAO,CAAC;MACnF,IAAIK,QAAQ,CAACE,IAAI,CAACrC,OAAO,EAAE;QACvBC,UAAU,CAAC,0BAA0B,CAAC;QACtCgC,UAAU,CAAC,CAAC;QACZV,mBAAmB,CAAC,KAAK,CAAC;QAC1BQ,UAAU,CAAC;UACPd,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE,EAAE;UACTa,QAAQ,EAAE,EAAE;UACZZ,aAAa,EAAE,EAAE;UACjBC,OAAO,EAAE;QACb,CAAC,CAAC;QACF;QACA8C,UAAU,CAAC,MAAMlE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACoC,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,oBAAoB,CAAC;QACvDgB,UAAU,CAAC,MAAMpE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqD,GAAG,EAAE;MACVrD,QAAQ,CAAC,oBAAoB,CAAC;MAC9BoE,UAAU,CAAC,MAAMpE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC;EACJ,CAAC;EAED,MAAM4E,qBAAqB,GAAG,MAAO9B,IAAI,IAAK;IAC1ChB,qBAAqB,CAAC,IAAI,CAAC;IAC3BJ,uBAAuB,CAAC,IAAI,CAAC;IAC7B,IAAI;MACA,MAAMU,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,GAAG,CAAC,GAAG/C,YAAY,oCAAoCwD,IAAI,CAACiB,OAAO,EAAE,CAAC;MACnG,IAAI3B,QAAQ,CAACE,IAAI,CAACrC,OAAO,EAAE;QACvB2B,sBAAsB,CAACQ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAC9C,CAAC,MAAM;QACHtC,QAAQ,CAACoC,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,8BAA8B,CAAC;QACjE1B,uBAAuB,CAAC,KAAK,CAAC;QAC9B0C,UAAU,CAAC,MAAMpE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqD,GAAG,EAAE;MACVrD,QAAQ,CAAC,8BAA8B,CAAC;MACxC0B,uBAAuB,CAAC,KAAK,CAAC;MAC9B0C,UAAU,CAAC,MAAMpE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACpCsD,OAAO,CAACvD,KAAK,CAAC,8BAA8B,EAAEsD,GAAG,CAAC;IACtD,CAAC,SAAS;MACNvB,qBAAqB,CAAC,KAAK,CAAC;IAChC;EACJ,CAAC;EAED,MAAM+C,iBAAiB,GAAG,MAAO/B,IAAI,IAAK;IACtC,IAAIgC,MAAM,CAACC,OAAO,CAAC,0CAA0CjC,IAAI,CAAC5B,QAAQ,yDAAyD,CAAC,EAAE;MAClI,IAAI;QACAoC,OAAO,CAAC0B,GAAG,CAAC,kBAAkB,EAAElC,IAAI,CAACiB,OAAO,CAAC,CAAC,CAAC;QAC/C,MAAM3B,QAAQ,GAAG,MAAMhE,KAAK,CAACuG,IAAI,CAAC,GAAGrF,YAAY,4BAA4B,EAAE;UAC3EyE,OAAO,EAAEjB,IAAI,CAACiB,OAAO;UACrBkB,MAAM,EAAE;QACZ,CAAC,CAAC;QACF3B,OAAO,CAAC0B,GAAG,CAAC,mBAAmB,EAAE5C,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;QACjD,IAAIF,QAAQ,CAACE,IAAI,CAACrC,OAAO,EAAE;UACvBC,UAAU,CAAC,SAAS4C,IAAI,CAAC5B,QAAQ,2BAA2B,CAAC;UAC7DgB,UAAU,CAAC,CAAC;UACZkC,UAAU,CAAC,MAAMlE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QAC1C,CAAC,MAAM;UACHF,QAAQ,CAACoC,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,wBAAwB,CAAC;UAC3DgB,UAAU,CAAC,MAAMpE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACxC;MACJ,CAAC,CAAC,OAAOqD,GAAG,EAAE;QAAA,IAAA6B,aAAA,EAAAC,kBAAA;QACV7B,OAAO,CAACvD,KAAK,CAAC,gBAAgB,EAAEsD,GAAG,CAAC,CAAC,CAAC;QACtCrD,QAAQ,CAAC,0BAA0B,IAAI,EAAAkF,aAAA,GAAA7B,GAAG,CAACjB,QAAQ,cAAA8C,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAc5C,IAAI,cAAA6C,kBAAA,uBAAlBA,kBAAA,CAAoB/B,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;QACnFgB,UAAU,CAAC,MAAMpE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ;EACJ,CAAC;EAED,MAAMoF,aAAa,GAAG,MAAOtC,IAAI,IAAK;IAClC,IAAIgC,MAAM,CAACC,OAAO,CAAC,sCAAsCjC,IAAI,CAAC5B,QAAQ,iGAAiG,CAAC,EAAE;MACtK,IAAI;QACAoC,OAAO,CAAC0B,GAAG,CAAC,eAAe,EAAElC,IAAI,CAACiB,OAAO,CAAC,CAAC,CAAC;QAC5C,MAAM3B,QAAQ,GAAG,MAAMhE,KAAK,CAACuG,IAAI,CAAC,GAAGrF,YAAY,wBAAwB,EAAE;UACvEyE,OAAO,EAAEjB,IAAI,CAACiB,OAAO;UACrBkB,MAAM,EAAE;QACZ,CAAC,CAAC;QACF3B,OAAO,CAAC0B,GAAG,CAAC,eAAe,EAAE5C,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;QAC7C,IAAIF,QAAQ,CAACE,IAAI,CAACrC,OAAO,EAAE;UACvBC,UAAU,CAAC,SAAS4C,IAAI,CAAC5B,QAAQ,wBAAwB,CAAC;UAC1DgB,UAAU,CAAC,CAAC;UACZkC,UAAU,CAAC,MAAMlE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QAC1C,CAAC,MAAM;UACHF,QAAQ,CAACoC,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,oBAAoB,CAAC;UACvDgB,UAAU,CAAC,MAAMpE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACxC;MACJ,CAAC,CAAC,OAAOqD,GAAG,EAAE;QAAA,IAAAgC,cAAA,EAAAC,mBAAA;QACVhC,OAAO,CAACvD,KAAK,CAAC,YAAY,EAAEsD,GAAG,CAAC,CAAC,CAAC;QAClCrD,QAAQ,CAAC,sBAAsB,IAAI,EAAAqF,cAAA,GAAAhC,GAAG,CAACjB,QAAQ,cAAAiD,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc/C,IAAI,cAAAgD,mBAAA,uBAAlBA,mBAAA,CAAoBlC,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;QAC/EgB,UAAU,CAAC,MAAMpE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ;EACJ,CAAC;;EAED;EACA,MAAMuF,YAAY,GAAItB,CAAC,IAAK;IACxB3D,aAAa,CAAC2D,CAAC,CAACM,MAAM,CAACD,KAAK,CAAC;IAC7B9D,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;;EAED;EACA,MAAMgF,aAAa,GAAG7F,KAAK,CAACkD,MAAM,CAACC,IAAI,IACnCA,IAAI,CAAC5B,QAAQ,CAACuE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrF,UAAU,CAACoF,WAAW,CAAC,CAAC,CAAC,IAC9D3C,IAAI,CAAC3B,SAAS,CAACsE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrF,UAAU,CAACoF,WAAW,CAAC,CAAC,CAAC,IAC/D3C,IAAI,CAAC1B,KAAK,CAACqE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrF,UAAU,CAACoF,WAAW,CAAC,CAAC,CAC9D,CAAC;;EAED;EACA,MAAME,eAAe,GAAGpF,WAAW,GAAGE,YAAY;EAClD,MAAMmF,gBAAgB,GAAGD,eAAe,GAAGlF,YAAY;EACvD,MAAMoF,YAAY,GAAGL,aAAa,CAACM,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAC3E,MAAMI,UAAU,GAAG/C,IAAI,CAACgD,IAAI,CAACR,aAAa,CAAC5C,MAAM,GAAGnC,YAAY,CAAC;EAEjE,MAAMwF,QAAQ,GAAIC,UAAU,IAAK1F,cAAc,CAAC0F,UAAU,CAAC;;EAE3D;EACA,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIL,UAAU,EAAEK,CAAC,EAAE,EAAE;IAClCD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;EACvB;EAEA,oBACIjH,OAAA;IAAKmH,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAExCpH,OAAA;MAAKmH,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBpH,OAAA;QAAImH,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrExH,OAAA;QAAGmH,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,EAGL5G,KAAK,iBACFZ,OAAA;MAAKmH,SAAS,EAAC,+EAA+E;MAACM,IAAI,EAAC,OAAO;MAAAL,QAAA,eACvGpH,OAAA;QAAMmH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAExG;MAAK;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CACR,EACA1G,OAAO,iBACJd,OAAA;MAAKmH,SAAS,EAAC,qFAAqF;MAACM,IAAI,EAAC,OAAO;MAAAL,QAAA,eAC7GpH,OAAA;QAAMmH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEtG;MAAO;QAAAuG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CACR,eAGDxH,OAAA;MAAKmH,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAEtEpH,OAAA;QAAKmH,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEpH,OAAA;UAAKmH,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAC9CpH,OAAA,CAACd,OAAO;YAACiI,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNxH,OAAA;UAAAoH,QAAA,gBACIpH,OAAA;YAAGmH,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7ExH,OAAA;YAAImH,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE7F,KAAK,CAACE;UAAU;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxH,OAAA;QAAKmH,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEpH,OAAA;UAAKmH,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eAC/CpH,OAAA,CAACZ,WAAW;YAAC+H,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNxH,OAAA;UAAAoH,QAAA,gBACIpH,OAAA;YAAGmH,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9ExH,OAAA;YAAImH,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE7F,KAAK,CAACG;UAAW;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxH,OAAA;QAAKmH,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEpH,OAAA;UAAKmH,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAChDpH,OAAA,CAACb,UAAU;YAACgI,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNxH,OAAA;UAAAoH,QAAA,gBACIpH,OAAA;YAAGmH,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFxH,OAAA;YAAImH,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE7F,KAAK,CAACI;UAAQ;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxH,OAAA;QAAKmH,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEpH,OAAA;UAAKmH,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAChDpH,OAAA,CAACX,WAAW;YAAC8H,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNxH,OAAA;UAAAoH,QAAA,gBACIpH,OAAA;YAAGmH,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/ExH,OAAA;YAAImH,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE7F,KAAK,CAACK;UAAY;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNxH,OAAA;MAAKmH,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACnDpH,OAAA;QAAKmH,SAAS,EAAC,mEAAmE;QAAAC,QAAA,gBAC9EpH,OAAA;UAAImH,SAAS,EAAC,6EAA6E;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1GxH,OAAA;UAAKmH,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAC1FpH,OAAA;YACI0H,OAAO,EAAEA,CAAA,KAAMrF,mBAAmB,CAAC,IAAI,CAAE;YACzC8E,SAAS,EAAC,mFAAmF;YAAAC,QAAA,gBAE7FpH,OAAA,CAACb,UAAU;cAACgI,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxH,OAAA;YAAKmH,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACpCpH,OAAA;cAAKmH,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACjFpH,OAAA,CAACT,QAAQ;gBAAC4H,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNxH,OAAA;cACI2H,IAAI,EAAC,MAAM;cACXR,SAAS,EAAC,qJAAqJ;cAC/JS,WAAW,EAAC,iBAAiB;cAC7BzC,KAAK,EAAEjE,UAAW;cAClB2G,QAAQ,EAAEzB;YAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxH,OAAA;QAAKmH,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACjCpH,OAAA;UAAOmH,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpH,OAAA;YAAOmH,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BpH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAI8H,KAAK,EAAC,KAAK;gBAACX,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxH,OAAA;gBAAI8H,KAAK,EAAC,KAAK;gBAACX,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,EAAC;cAE9H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxH,OAAA;gBAAI8H,KAAK,EAAC,KAAK;gBAACX,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxH,OAAA;gBAAI8H,KAAK,EAAC,KAAK;gBAACX,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxH,OAAA;gBAAI8H,KAAK,EAAC,KAAK;gBAACX,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,EAAC;cAE/H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxH,OAAA;gBAAI8H,KAAK,EAAC,KAAK;gBAACX,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,EAAC;cAE/H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxH,OAAA;gBAAI8H,KAAK,EAAC,KAAK;gBAACX,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,EAAC;cAE9H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxH,OAAA;gBAAI8H,KAAK,EAAC,KAAK;gBAACX,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,EAAC;cAEnI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxH,OAAA;gBAAI8H,KAAK,EAAC,KAAK;gBAACX,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,EAAC;cAE3H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRxH,OAAA;YAAOmH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/CV,YAAY,CAACqB,GAAG,CAAC,CAACpE,IAAI,EAAEqE,KAAK,kBAC1BhI,OAAA;cAAuBmH,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/CpH,OAAA;gBAAImH,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC5DX,gBAAgB,GAAGuB,KAAK,GAAG;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACLxH,OAAA;gBAAImH,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAC7DpH,OAAA;kBAAKmH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAC9BpH,OAAA;oBACIiI,GAAG,EAAE7D,WAAW,CAACT,IAAI,CAACzB,aAAa,CAAC,IAAIwC,gBAAgB,CAAC,CAAE;oBAC3DwD,GAAG,EAAEvE,IAAI,CAACzB,aAAa,IAAI,gBAAiB;oBAC5CiF,SAAS,EAAC,8DAA8D;oBACxEgB,OAAO,EAAGrD,CAAC,IAAK;sBACZA,CAAC,CAACM,MAAM,CAAC6C,GAAG,GAAGvD,gBAAgB,CAAC,CAAC;oBACrC;kBAAE;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACLxH,OAAA;gBAAImH,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvCpH,OAAA;kBAAKmH,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEzD,IAAI,CAAC5B;gBAAQ;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACLxH,OAAA;gBAAImH,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvCpH,OAAA;kBAAMmH,SAAS,EAAE,4DACbxD,IAAI,CAACK,MAAM,KAAK,QAAQ,GAAG,yBAAyB,GACpDL,IAAI,CAACK,MAAM,KAAK,WAAW,GAAG,+BAA+B,GAC7D,6BAA6B,EAC9B;kBAAAoD,QAAA,EACEzD,IAAI,CAACK,MAAM,KAAK,QAAQ,GAAG,QAAQ,GACnCL,IAAI,CAACK,MAAM,KAAK,WAAW,GAAG,WAAW,GACzC;gBAAQ;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACLxH,OAAA;gBAAImH,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,eAC9DpH,OAAA;kBAAKmH,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEzD,IAAI,CAAC3B;gBAAS;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACLxH,OAAA;gBAAImH,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,eAC9DpH,OAAA;kBAAKmH,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEzD,IAAI,CAAC1B;gBAAK;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACLxH,OAAA;gBAAImH,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAC7DpH,OAAA;kBAAKmH,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GACvCzD,IAAI,CAACzB,aAAa,IAAIkC,WAAW,CAACT,IAAI,CAACzB,aAAa,CAAC,iBAClDlC,OAAA;oBACIiI,GAAG,EAAE7D,WAAW,CAACT,IAAI,CAACzB,aAAa,CAAE;oBACrCgG,GAAG,EAAEvE,IAAI,CAACzB,aAAc;oBACxBiF,SAAS,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CACJ,eACDxH,OAAA;oBAAKmH,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEzD,IAAI,CAACzB,aAAa,IAAI;kBAAkB;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACLxH,OAAA;gBAAImH,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eAClEpH,OAAA;kBAAKmH,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAEzD,IAAI,CAACxB,OAAO,EAAC,KAAG;gBAAA;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACLxH,OAAA;gBAAImH,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,eAC9EpH,OAAA;kBAAKmH,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BpH,OAAA;oBACI0H,OAAO,EAAEA,CAAA,KAAMjC,qBAAqB,CAAC9B,IAAI,CAAE;oBAC3CwD,SAAS,EAAC,yCAAyC;oBACnDiB,KAAK,EAAC,cAAc;oBAAAhB,QAAA,eAEpBpH,OAAA,CAACR,KAAK;sBAAA6H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACTxH,OAAA;oBACI0H,OAAO,EAAEA,CAAA,KAAM/C,UAAU,CAAChB,IAAI,CAAE;oBAChCwD,SAAS,EAAC,uCAAuC;oBACjDiB,KAAK,EAAC,WAAW;oBAAAhB,QAAA,eAEjBpH,OAAA,CAACV,MAAM;sBAAA+H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACTxH,OAAA;oBACI0H,OAAO,EAAEA,CAAA,KAAMhC,iBAAiB,CAAC/B,IAAI,CAAE;oBACvCwD,SAAS,EAAC,2CAA2C;oBACrDiB,KAAK,EAAC,cAAc;oBAAAhB,QAAA,eAEpBpH,OAAA,CAACP,WAAW;sBAAA4H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACTxH,OAAA;oBACI0H,OAAO,EAAEA,CAAA,KAAMzB,aAAa,CAACtC,IAAI,CAAE;oBACnCwD,SAAS,EAAC,qCAAqC;oBAC/CiB,KAAK,EAAC,UAAU;oBAAAhB,QAAA,eAEhBpH,OAAA,CAACN,KAAK;sBAAA2H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAlFA7D,IAAI,CAACiB,OAAO;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmFjB,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAGLZ,UAAU,GAAG,CAAC,iBACX5G,OAAA;QAAKmH,SAAS,EAAC,mFAAmF;QAAAC,QAAA,eAC9FpH,OAAA;UAAKmH,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBACxEpH,OAAA;YAAAoH,QAAA,eACIpH,OAAA;cAAGmH,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,UACzB,eAAApH,OAAA;gBAAMmH,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEX,gBAAgB,GAAG;cAAC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,OAAG,EAAC,GAAG,eAC1ExH,OAAA;gBAAMmH,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACxBZ,eAAe,GAAGH,aAAa,CAAC5C,MAAM,GAAG4C,aAAa,CAAC5C,MAAM,GAAG+C;cAAe;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,EAAC,GAAG,EAAC,KACT,eAAAxH,OAAA;gBAAMmH,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEf,aAAa,CAAC5C;cAAM;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,YAClE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxH,OAAA;YAAAoH,QAAA,eACIpH,OAAA;cAAKmH,SAAS,EAAC,2DAA2D;cAAC,cAAW,YAAY;cAAAC,QAAA,gBAC9FpH,OAAA;gBACI0H,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC1F,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,GAAG,CAAC,CAAE;gBAC/DiH,QAAQ,EAAEjH,WAAW,KAAK,CAAE;gBAC5B+F,SAAS,EAAE,gHACP/F,WAAW,KAAK,CAAC,GAAG,kCAAkC,GAAG,gCAAgC,EAC1F;gBAAAgG,QAAA,gBAEHpH,OAAA;kBAAMmH,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzCxH,OAAA;kBAAKmH,SAAS,EAAC,SAAS;kBAACmB,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAC,eAAY,MAAM;kBAAApB,QAAA,eAClHpH,OAAA;oBAAMyI,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,mHAAmH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAERR,WAAW,CAACe,GAAG,CAACa,MAAM,iBACnB5I,OAAA;gBAEI0H,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC8B,MAAM,CAAE;gBAChCzB,SAAS,EAAE,mGACP/F,WAAW,KAAKwH,MAAM,GAChB,+CAA+C,GAC/C,gCAAgC,EACvC;gBAAAxB,QAAA,EAEFwB;cAAM,GARFA,MAAM;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASP,CACX,CAAC,eAEFxH,OAAA;gBACI0H,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC1F,WAAW,GAAGwF,UAAU,GAAGxF,WAAW,GAAG,CAAC,GAAGwF,UAAU,CAAE;gBACjFyB,QAAQ,EAAEjH,WAAW,KAAKwF,UAAW;gBACrCO,SAAS,EAAE,gHACP/F,WAAW,KAAKwF,UAAU,GAAG,kCAAkC,GAAG,gCAAgC,EACnG;gBAAAQ,QAAA,gBAEHpH,OAAA;kBAAMmH,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrCxH,OAAA;kBAAKmH,SAAS,EAAC,SAAS;kBAACmB,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAC,eAAY,MAAM;kBAAApB,QAAA,eAClHpH,OAAA;oBAAMyI,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,oHAAoH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGLxG,aAAa,iBACVhB,OAAA;MAAKmH,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC3FpH,OAAA;QAAKmH,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC1DpH,OAAA;UAAKmH,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC3DpH,OAAA;YAAImH,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClExH,OAAA;YACImH,SAAS,EAAC,+DAA+D;YACzEO,OAAO,EAAEA,CAAA,KAAMzG,gBAAgB,CAAC,IAAI,CAAE;YAAAmG,QAAA,EACzC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENxH,OAAA;UAAM6I,QAAQ,EAAEhE,YAAa;UAACsC,SAAS,EAAC,KAAK;UAAAC,QAAA,gBACzCpH,OAAA;YAAKmH,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBpH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFxH,OAAA;gBACI2H,IAAI,EAAC,MAAM;gBACXnD,IAAI,EAAC,UAAU;gBACfW,KAAK,EAAEtD,WAAW,CAACE,QAAS;gBAC5B8F,QAAQ,EAAE3C,iBAAkB;gBAC5BiC,SAAS,EAAC,4IAA4I;gBACtJ2B,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjFxH,OAAA;gBACI2H,IAAI,EAAC,MAAM;gBACXnD,IAAI,EAAC,WAAW;gBAChBW,KAAK,EAAEtD,WAAW,CAACG,SAAU;gBAC7B6F,QAAQ,EAAE3C,iBAAkB;gBAC5BiC,SAAS,EAAC,4IAA4I;gBACtJ2B,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7ExH,OAAA;gBACI2H,IAAI,EAAC,OAAO;gBACZnD,IAAI,EAAC,OAAO;gBACZW,KAAK,EAAEtD,WAAW,CAACI,KAAM;gBACzB4F,QAAQ,EAAE3C,iBAAkB;gBAC5BiC,SAAS,EAAC,4IAA4I;gBACtJ2B,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFxH,OAAA;gBACIwE,IAAI,EAAC,eAAe;gBACpBW,KAAK,EAAEtD,WAAW,CAACK,aAAc;gBACjC2F,QAAQ,EAAE3C,iBAAkB;gBAC5BiC,SAAS,EAAC,4IAA4I;gBACtJ2B,QAAQ;gBAAA1B,QAAA,gBAERpH,OAAA;kBAAQmF,KAAK,EAAC,EAAE;kBAAAiC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7C9G,KAAK,CAACqH,GAAG,CAACzD,IAAI,iBACXtE,OAAA;kBAAsBmF,KAAK,EAAEb,IAAI,CAACE,IAAK;kBAAA4C,QAAA,EAAE9C,IAAI,CAACE;gBAAI,GAArCF,IAAI,CAACyE,EAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAuC,CAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAENxH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1FxH,OAAA;gBACI2H,IAAI,EAAC,QAAQ;gBACbnD,IAAI,EAAC,SAAS;gBACdW,KAAK,EAAEtD,WAAW,CAACM,OAAQ;gBAC3B0F,QAAQ,EAAE3C,iBAAkB;gBAC5BiC,SAAS,EAAC,4IAA4I;gBACtJ2B,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENxH,OAAA;YAAKmH,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC5CpH,OAAA;cACI2H,IAAI,EAAC,QAAQ;cACbD,OAAO,EAAEA,CAAA,KAAMzG,gBAAgB,CAAC,IAAI,CAAE;cACtCkG,SAAS,EAAC,2LAA2L;cAAAC,QAAA,EACxM;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxH,OAAA;cACI2H,IAAI,EAAC,QAAQ;cACbR,SAAS,EAAC,+LAA+L;cAAAC,QAAA,EAC5M;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAGApF,gBAAgB,iBACbpC,OAAA;MAAKmH,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC3FpH,OAAA;QAAKmH,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC1DpH,OAAA;UAAKmH,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC3DpH,OAAA;YAAImH,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrExH,OAAA;YACImH,SAAS,EAAC,+DAA+D;YACzEO,OAAO,EAAEA,CAAA,KAAMrF,mBAAmB,CAAC,KAAK,CAAE;YAAA+E,QAAA,EAC7C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENxH,OAAA;UAAM6I,QAAQ,EAAEtD,aAAc;UAAC4B,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAC1CpH,OAAA;YAAKmH,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBpH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFxH,OAAA;gBACI2H,IAAI,EAAC,MAAM;gBACXnD,IAAI,EAAC,UAAU;gBACfW,KAAK,EAAEvC,OAAO,CAACb,QAAS;gBACxB8F,QAAQ,EAAEvC,wBAAyB;gBACnC6B,SAAS,EAAC,8IAA8I;gBACxJ2B,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjFxH,OAAA;gBACI2H,IAAI,EAAC,MAAM;gBACXnD,IAAI,EAAC,WAAW;gBAChBW,KAAK,EAAEvC,OAAO,CAACZ,SAAU;gBACzB6F,QAAQ,EAAEvC,wBAAyB;gBACnC6B,SAAS,EAAC,8IAA8I;gBACxJ2B,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7ExH,OAAA;gBACI2H,IAAI,EAAC,OAAO;gBACZnD,IAAI,EAAC,OAAO;gBACZW,KAAK,EAAEvC,OAAO,CAACX,KAAM;gBACrB4F,QAAQ,EAAEvC,wBAAyB;gBACnC6B,SAAS,EAAC,8IAA8I;gBACxJ2B,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFxH,OAAA;gBACI2H,IAAI,EAAC,UAAU;gBACfnD,IAAI,EAAC,UAAU;gBACfW,KAAK,EAAEvC,OAAO,CAACE,QAAS;gBACxB+E,QAAQ,EAAEvC,wBAAyB;gBACnC6B,SAAS,EAAC,8IAA8I;gBACxJ2B,QAAQ;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFxH,OAAA;gBACIwE,IAAI,EAAC,eAAe;gBACpBW,KAAK,EAAEvC,OAAO,CAACV,aAAc;gBAC7B2F,QAAQ,EAAEvC,wBAAyB;gBACnC6B,SAAS,EAAC,8IAA8I;gBAAAC,QAAA,gBAExJpH,OAAA;kBAAQmF,KAAK,EAAC,EAAE;kBAAAiC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7C9G,KAAK,CAACqH,GAAG,CAACzD,IAAI,iBACXtE,OAAA;kBAAsBmF,KAAK,EAAEb,IAAI,CAACE,IAAK;kBAAA4C,QAAA,EAAE9C,IAAI,CAACE;gBAAI,GAArCF,IAAI,CAACyE,EAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAuC,CAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAENxH,OAAA;cAAAoH,QAAA,gBACIpH,OAAA;gBAAOmH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClGxH,OAAA;gBACI2H,IAAI,EAAC,QAAQ;gBACbnD,IAAI,EAAC,SAAS;gBACdW,KAAK,EAAEvC,OAAO,CAACT,OAAQ;gBACvB0F,QAAQ,EAAEvC,wBAAyB;gBACnC6B,SAAS,EAAC,8IAA8I;gBACxJ6B,GAAG,EAAC;cAAG;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENxH,OAAA;YAAKmH,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC5CpH,OAAA;cACI2H,IAAI,EAAC,QAAQ;cACbD,OAAO,EAAEA,CAAA,KAAMrF,mBAAmB,CAAC,KAAK,CAAE;cAC1C8E,SAAS,EAAC,4LAA4L;cAAAC,QAAA,EACzM;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxH,OAAA;cACI2H,IAAI,EAAC,QAAQ;cACbR,SAAS,EAAC,kMAAkM;cAAAC,QAAA,EAC/M;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAGAlF,oBAAoB,iBACjBtC,OAAA;MAAKmH,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC3FpH,OAAA;QAAKmH,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBAE/CpH,OAAA;UAAKmH,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAC/BpH,OAAA;YACImH,SAAS,EAAC,wFAAwF;YAClGO,OAAO,EAAEA,CAAA,KAAM;cACXnF,uBAAuB,CAAC,KAAK,CAAC;cAC9BE,sBAAsB,CAAC,IAAI,CAAC;YAChC,CAAE;YAAA2E,QAAA,EACL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAER9E,kBAAkB,gBACf1C,OAAA;YAAKmH,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACvCpH,OAAA;cAAKmH,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CxH,OAAA;cAAMmH,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,GACNhF,mBAAmB,gBACnBxC,OAAA,CAAAE,SAAA;YAAAkH,QAAA,gBACIpH,OAAA;cAAKmH,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eAC/BpH,OAAA;gBACIiI,GAAG,EAAE7D,WAAW,CAAC5B,mBAAmB,CAACN,aAAa,CAAC,IAAIwC,gBAAgB,CAAC,CAAE;gBAC1EwD,GAAG,EAAE1F,mBAAmB,CAACN,aAAa,IAAI,eAAgB;gBAC1DiG,OAAO,EAAGrD,CAAC,IAAK;kBACZA,CAAC,CAACM,MAAM,CAAC6C,GAAG,GAAGvD,gBAAgB,CAAC,CAAC;gBACrC;cAAE;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNxH,OAAA;cAAKmH,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAE5E,mBAAmB,CAACR;YAAS;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvExH,OAAA;cAAKmH,SAAS,EAAC,yBAAyB;cAAAC,QAAA,GAAC,GAAC,EAAC5E,mBAAmB,CAACT,QAAQ;YAAA;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9ExH,OAAA;cAAKmH,SAAS,EAAE,sBACZ3E,mBAAmB,CAACwB,MAAM,KAAK,QAAQ,GAAG,YAAY,GACtDxB,mBAAmB,CAACwB,MAAM,KAAK,WAAW,GAAG,eAAe,GAC5D,cAAc,EACf;cAAAoD,QAAA,GACE,EAAA9G,qBAAA,GAAAkC,mBAAmB,CAACwB,MAAM,cAAA1D,qBAAA,uBAA1BA,qBAAA,CAA4B2I,WAAW,CAAC,CAAC,KAAI,QAAQ,EAAC,SAC3D;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA,eACR,CAAC,GACH,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGNxH,OAAA;UAAKmH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5B,CAAC1E,kBAAkB,IAAIF,mBAAmB,iBACvCxC,OAAA;YAAKmH,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAEtBpH,OAAA;cAAKmH,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC7BpH,OAAA;gBAAKmH,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC7BpH,OAAA;kBAAKmH,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAE5E,mBAAmB,CAAC0G,UAAU,IAAI;gBAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7FxH,OAAA;kBAAKmH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACNxH,OAAA;gBAAKmH,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC7BpH,OAAA;kBAAKmH,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE5E,mBAAmB,CAAC2G,IAAI,IAAI;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxFxH,OAAA;kBAAKmH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNxH,OAAA;gBAAKmH,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC7BpH,OAAA;kBAAKmH,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAE5E,mBAAmB,CAAC4G,KAAK,IAAI;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1FxH,OAAA;kBAAKmH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNxH,OAAA;gBAAKmH,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC7BpH,OAAA;kBAAKmH,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAE5E,mBAAmB,CAAC6G,MAAM,IAAI;gBAAC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxFxH,OAAA;kBAAKmH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNxH,OAAA;cAAKmH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9BpH,OAAA;gBAAKmH,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BpH,OAAA;kBAAMmH,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClDxH,OAAA;kBAAMmH,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAAC,GAAC,EAAC5E,mBAAmB,CAACoC,OAAO;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eACNxH,OAAA;gBAAKmH,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BpH,OAAA;kBAAMmH,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CxH,OAAA;kBAAMmH,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAE5E,mBAAmB,CAACP;gBAAK;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNxH,OAAA;gBAAKmH,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BpH,OAAA;kBAAMmH,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CxH,OAAA;kBAAMmH,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE5E,mBAAmB,CAACiF;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACNxH,OAAA;gBAAKmH,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BpH,OAAA;kBAAMmH,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtDxH,OAAA;kBAAMmH,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAE5E,mBAAmB,CAACN,aAAa,IAAI;gBAAM;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNxH,OAAA;cAAKmH,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBAE/BpH,OAAA;gBAAKmH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC9BpH,OAAA;kBAAKmH,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAChCpH,OAAA;oBAAKmH,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DxH,OAAA,CAACJ,OAAO;oBAACuH,SAAS,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNxH,OAAA;kBAAKmH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BpH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjDxH,OAAA;sBAAKmH,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAAE5E,mBAAmB,CAACL,OAAO,EAAC,KAAG;oBAAA;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,eACNxH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAE5E,mBAAmB,CAAC8G,MAAM,IAAI;oBAAC;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACNxH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAE5E,mBAAmB,CAAC+G,YAAY,IAAI;oBAAC;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,eACNxH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAE5E,mBAAmB,CAACgH,kBAAkB,IAAI;oBAAC;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNxH,OAAA;gBAAKmH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC9BpH,OAAA;kBAAKmH,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAChCpH,OAAA;oBAAKmH,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrDxH,OAAA,CAACL,QAAQ;oBAACwH,SAAS,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNxH,OAAA;kBAAKmH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BpH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAC9B5E,mBAAmB,CAAC0G,UAAU,GAAG,CAAC,GAC7B,GAAGrF,IAAI,CAAC4F,KAAK,CAAEjH,mBAAmB,CAAC2G,IAAI,GAAG3G,mBAAmB,CAAC0G,UAAU,GAAI,GAAG,CAAC,GAAG,GACnF;oBAAI;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAET,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNxH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAE5E,mBAAmB,CAACkH,cAAc,IAAI;oBAAC;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,eACNxH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAE5E,mBAAmB,CAACmH,cAAc,IAAI;oBAAC;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,eACNxH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAE5E,mBAAmB,CAACoH,kBAAkB,IAAI;oBAAC;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNxH,OAAA;gBAAKmH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC9BpH,OAAA;kBAAKmH,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAChCpH,OAAA;oBAAKmH,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClDxH,OAAA,CAACF,aAAa;oBAACqH,SAAS,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACNxH,OAAA;kBAAKmH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BpH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAE,IAAI9D,IAAI,CAACd,mBAAmB,CAACuB,UAAU,CAAC,CAAC8F,kBAAkB,CAAC;oBAAC;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eACNxH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAC9B5E,mBAAmB,CAACsH,WAAW,GAC5B,IAAIxG,IAAI,CAACd,mBAAmB,CAACsH,WAAW,CAAC,CAACD,kBAAkB,CAAC,CAAC,GAC9D;oBAAO;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNxH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAC9B5E,mBAAmB,CAACuH,aAAa,GAC9B,IAAIzG,IAAI,CAACd,mBAAmB,CAACuH,aAAa,CAAC,CAACF,kBAAkB,CAAC,CAAC,GAChE;oBAAO;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNxH,OAAA;oBAAKmH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrDxH,OAAA;sBAAKmH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAE5E,mBAAmB,CAACwH,iBAAiB,IAAI;oBAAC;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGNxH,OAAA;UAAKmH,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eACxEpH,OAAA;YACI0H,OAAO,EAAEA,CAAA,KAAM;cACXnF,uBAAuB,CAAC,KAAK,CAAC;cAC9BE,sBAAsB,CAAC,IAAI,CAAC;YAChC,CAAE;YACF0E,SAAS,EAAC,yGAAyG;YACnH8C,KAAK,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAA9C,QAAA,EACzC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAeA,CAAC;AAEd;AAACnH,EAAA,CA/8BQD,cAAc;EAAA,QACFpB,WAAW;AAAA;AAAAmL,EAAA,GADvB/J,cAAc;AAg9BvB,eAAeA,cAAc;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}