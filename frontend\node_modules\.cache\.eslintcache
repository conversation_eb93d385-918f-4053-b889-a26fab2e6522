[{"C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js": "1", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js": "2", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js": "3", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js": "4", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js": "5", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js": "6", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js": "7", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js": "8", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js": "9", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js": "10", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js": "11", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js": "12", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js": "13", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js": "14", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js": "15", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js": "16", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js": "17", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js": "18", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js": "19", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js": "20", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js": "21", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js": "22", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js": "23", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js": "24", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js": "25", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js": "26", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js": "27", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js": "28", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js": "29", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js": "30", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js": "31", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js": "32", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js": "33", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js": "34", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js": "35", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js": "36", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js": "37", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js": "38", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js": "39", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js": "40", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js": "41", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js": "42", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js": "43", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js": "44", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js": "45", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js": "46", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js": "47", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js": "48", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js": "49", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js": "50", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js": "51", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js": "52", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js": "53", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js": "54", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js": "55", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js": "56", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js": "57", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js": "58", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js": "59", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js": "60", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js": "61", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js": "62", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js": "63", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js": "64", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js": "65", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js": "66", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js": "67", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js": "68", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js": "69", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js": "70", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js": "71", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js": "72", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js": "73", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js": "74", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js": "75", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js": "76", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js": "77", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js": "78", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js": "79", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js": "80", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js": "81", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js": "82", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js": "83", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js": "84", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js": "85", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js": "86", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js": "87", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js": "88", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js": "89"}, {"size": 1593, "mtime": 1739215325917, "results": "90", "hashOfConfig": "91"}, {"size": 11192, "mtime": 1749283813001, "results": "92", "hashOfConfig": "91"}, {"size": 362, "mtime": 1725527312699, "results": "93", "hashOfConfig": "91"}, {"size": 2707, "mtime": 1749107911322, "results": "94", "hashOfConfig": "91"}, {"size": 1583, "mtime": 1738380484748, "results": "95", "hashOfConfig": "91"}, {"size": 1958, "mtime": 1738907546846, "results": "96", "hashOfConfig": "91"}, {"size": 1431, "mtime": 1747472215947, "results": "97", "hashOfConfig": "91"}, {"size": 17038, "mtime": 1745585449129, "results": "98", "hashOfConfig": "91"}, {"size": 5850, "mtime": 1747430002225, "results": "99", "hashOfConfig": "91"}, {"size": 10946, "mtime": 1749283001145, "results": "100", "hashOfConfig": "91"}, {"size": 58869, "mtime": 1749289223101, "results": "101", "hashOfConfig": "91"}, {"size": 34669, "mtime": 1747471954224, "results": "102", "hashOfConfig": "91"}, {"size": 6588, "mtime": 1738406509501, "results": "103", "hashOfConfig": "91"}, {"size": 18493, "mtime": 1749277164795, "results": "104", "hashOfConfig": "91"}, {"size": 25409, "mtime": 1739175447004, "results": "105", "hashOfConfig": "91"}, {"size": 41023, "mtime": 1749288045343, "results": "106", "hashOfConfig": "91"}, {"size": 16128, "mtime": 1745598311818, "results": "107", "hashOfConfig": "91"}, {"size": 43308, "mtime": 1749118478833, "results": "108", "hashOfConfig": "91"}, {"size": 11677, "mtime": 1747481352521, "results": "109", "hashOfConfig": "91"}, {"size": 484, "mtime": 1747774257496, "results": "110", "hashOfConfig": "91"}, {"size": 3867, "mtime": 1749105548034, "results": "111", "hashOfConfig": "91"}, {"size": 13219, "mtime": 1747676927010, "results": "112", "hashOfConfig": "91"}, {"size": 14883, "mtime": 1738334800331, "results": "113", "hashOfConfig": "91"}, {"size": 6473, "mtime": 1747481379508, "results": "114", "hashOfConfig": "91"}, {"size": 398, "mtime": 1725625029363, "results": "115", "hashOfConfig": "91"}, {"size": 10599, "mtime": 1749283886752, "results": "116", "hashOfConfig": "91"}, {"size": 22573, "mtime": 1749277333189, "results": "117", "hashOfConfig": "91"}, {"size": 27005, "mtime": 1747687217258, "results": "118", "hashOfConfig": "91"}, {"size": 22327, "mtime": 1739035705052, "results": "119", "hashOfConfig": "91"}, {"size": 9112, "mtime": 1734251655762, "results": "120", "hashOfConfig": "91"}, {"size": 11975, "mtime": 1734248094632, "results": "121", "hashOfConfig": "91"}, {"size": 12979, "mtime": 1739086616323, "results": "122", "hashOfConfig": "91"}, {"size": 12071, "mtime": 1739001265026, "results": "123", "hashOfConfig": "91"}, {"size": 12037, "mtime": 1747670557722, "results": "124", "hashOfConfig": "91"}, {"size": 16681, "mtime": 1739089849375, "results": "125", "hashOfConfig": "91"}, {"size": 12891, "mtime": 1739078247803, "results": "126", "hashOfConfig": "91"}, {"size": 29746, "mtime": 1749282987484, "results": "127", "hashOfConfig": "91"}, {"size": 5324, "mtime": 1737964400994, "results": "128", "hashOfConfig": "91"}, {"size": 205, "mtime": 1732832805260, "results": "129", "hashOfConfig": "91"}, {"size": 28050, "mtime": 1738011980316, "results": "130", "hashOfConfig": "91"}, {"size": 30253, "mtime": 1737968307123, "results": "131", "hashOfConfig": "91"}, {"size": 8917, "mtime": 1738228976181, "results": "132", "hashOfConfig": "91"}, {"size": 1242, "mtime": 1732832820214, "results": "133", "hashOfConfig": "91"}, {"size": 1134, "mtime": 1732832829902, "results": "134", "hashOfConfig": "91"}, {"size": 1098, "mtime": 1732832839965, "results": "135", "hashOfConfig": "91"}, {"size": 11530, "mtime": 1732983571250, "results": "136", "hashOfConfig": "91"}, {"size": 23454, "mtime": 1738253936762, "results": "137", "hashOfConfig": "91"}, {"size": 24467, "mtime": 1732988420840, "results": "138", "hashOfConfig": "91"}, {"size": 4310, "mtime": 1734245942035, "results": "139", "hashOfConfig": "91"}, {"size": 5623, "mtime": 1734245958195, "results": "140", "hashOfConfig": "91"}, {"size": 3339, "mtime": 1734245925091, "results": "141", "hashOfConfig": "91"}, {"size": 6337, "mtime": 1736487062211, "results": "142", "hashOfConfig": "91"}, {"size": 5681, "mtime": 1734287339563, "results": "143", "hashOfConfig": "91"}, {"size": 10920, "mtime": 1739168463615, "results": "144", "hashOfConfig": "91"}, {"size": 14257, "mtime": 1739212427178, "results": "145", "hashOfConfig": "91"}, {"size": 16913, "mtime": 1738012431449, "results": "146", "hashOfConfig": "91"}, {"size": 21192, "mtime": 1738014939015, "results": "147", "hashOfConfig": "91"}, {"size": 3211, "mtime": 1747478622718, "results": "148", "hashOfConfig": "91"}, {"size": 6883, "mtime": 1749282918926, "results": "149", "hashOfConfig": "91"}, {"size": 1352, "mtime": 1738907631772, "results": "150", "hashOfConfig": "91"}, {"size": 591, "mtime": 1737714035353, "results": "151", "hashOfConfig": "91"}, {"size": 4889, "mtime": 1739089917990, "results": "152", "hashOfConfig": "91"}, {"size": 4026, "mtime": 1749114060143, "results": "153", "hashOfConfig": "91"}, {"size": 981, "mtime": 1747467742714, "results": "154", "hashOfConfig": "91"}, {"size": 597, "mtime": 1738005020143, "results": "155", "hashOfConfig": "91"}, {"size": 2649, "mtime": 1745558530865, "results": "156", "hashOfConfig": "91"}, {"size": 856, "mtime": 1738005002533, "results": "157", "hashOfConfig": "91"}, {"size": 778, "mtime": 1737703033090, "results": "158", "hashOfConfig": "91"}, {"size": 9268, "mtime": 1739089382382, "results": "159", "hashOfConfig": "91"}, {"size": 4473, "mtime": 1739114665777, "results": "160", "hashOfConfig": "91"}, {"size": 6511, "mtime": 1747772230646, "results": "161", "hashOfConfig": "91"}, {"size": 3561, "mtime": 1747465926259, "results": "162", "hashOfConfig": "91"}, {"size": 2058, "mtime": 1745560016985, "results": "163", "hashOfConfig": "91"}, {"size": 3270, "mtime": 1747683592095, "results": "164", "hashOfConfig": "91"}, {"size": 13040, "mtime": 1749112054896, "results": "165", "hashOfConfig": "91"}, {"size": 35666, "mtime": 1749231075399, "results": "166", "hashOfConfig": "91"}, {"size": 17158, "mtime": 1749113919875, "results": "167", "hashOfConfig": "91"}, {"size": 12332, "mtime": 1749106493490, "results": "168", "hashOfConfig": "91"}, {"size": 1631, "mtime": 1749111942679, "results": "169", "hashOfConfig": "91"}, {"size": 35110, "mtime": 1749118685592, "results": "170", "hashOfConfig": "91"}, {"size": 17861, "mtime": 1749116896225, "results": "171", "hashOfConfig": "91"}, {"size": 317, "mtime": 1749231241721, "results": "172", "hashOfConfig": "91"}, {"size": 22414, "mtime": 1749237247953, "results": "173", "hashOfConfig": "91"}, {"size": 15311, "mtime": 1749239054836, "results": "174", "hashOfConfig": "91"}, {"size": 19477, "mtime": 1749236876615, "results": "175", "hashOfConfig": "91"}, {"size": 12103, "mtime": 1749231233630, "results": "176", "hashOfConfig": "91"}, {"size": 26689, "mtime": 1749237355112, "results": "177", "hashOfConfig": "91"}, {"size": 16917, "mtime": 1749281261258, "results": "178", "hashOfConfig": "91"}, {"size": 3567, "mtime": 1749282608572, "results": "179", "hashOfConfig": "91"}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eamnk2", {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js", ["447"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js", ["448"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js", ["449", "450", "451"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js", ["452", "453"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js", ["454", "455", "456", "457", "458", "459", "460", "461", "462", "463"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js", ["464", "465"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js", ["466", "467", "468", "469", "470"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js", ["471"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js", ["472", "473"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js", ["474", "475", "476"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js", ["477"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js", ["478", "479"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js", ["480", "481", "482", "483"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js", ["484", "485", "486"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js", ["487", "488"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js", ["489"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js", ["490", "491", "492", "493", "494"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js", ["495"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js", ["496"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js", ["497", "498", "499", "500", "501"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js", ["502", "503", "504", "505", "506"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js", ["507"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js", ["508"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js", ["509"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js", ["510", "511"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js", ["512", "513", "514", "515"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js", ["516"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js", ["517", "518"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js", ["519"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js", ["520", "521", "522"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js", ["523"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js", ["524", "525", "526"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js", ["527"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js", ["528", "529"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js", ["530", "531", "532", "533"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js", ["534", "535"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js", ["536", "537"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js", [], [], {"ruleId": "538", "severity": 1, "message": "539", "line": 68, "column": 8, "nodeType": "540", "messageId": "541", "endLine": 68, "endColumn": 23}, {"ruleId": "542", "severity": 1, "message": "543", "line": 31, "column": 6, "nodeType": "544", "endLine": 31, "endColumn": 8, "suggestions": "545"}, {"ruleId": "542", "severity": 1, "message": "546", "line": 25, "column": 9, "nodeType": "547", "endLine": 25, "endColumn": 62}, {"ruleId": "542", "severity": 1, "message": "548", "line": 73, "column": 6, "nodeType": "544", "endLine": 73, "endColumn": 50, "suggestions": "549"}, {"ruleId": "542", "severity": 1, "message": "550", "line": 119, "column": 6, "nodeType": "544", "endLine": 119, "endColumn": 85, "suggestions": "551"}, {"ruleId": "538", "severity": 1, "message": "552", "line": 4, "column": 121, "nodeType": "540", "messageId": "541", "endLine": 4, "endColumn": 132}, {"ruleId": "538", "severity": 1, "message": "553", "line": 11, "column": 11, "nodeType": "540", "messageId": "541", "endLine": 11, "endColumn": 19}, {"ruleId": "538", "severity": 1, "message": "554", "line": 8, "column": 3, "nodeType": "540", "messageId": "541", "endLine": 8, "endColumn": 16}, {"ruleId": "538", "severity": 1, "message": "555", "line": 10, "column": 3, "nodeType": "540", "messageId": "541", "endLine": 10, "endColumn": 17}, {"ruleId": "538", "severity": 1, "message": "556", "line": 11, "column": 3, "nodeType": "540", "messageId": "541", "endLine": 11, "endColumn": 13}, {"ruleId": "538", "severity": 1, "message": "557", "line": 12, "column": 3, "nodeType": "540", "messageId": "541", "endLine": 12, "endColumn": 16}, {"ruleId": "538", "severity": 1, "message": "558", "line": 13, "column": 3, "nodeType": "540", "messageId": "541", "endLine": 13, "endColumn": 8}, {"ruleId": "538", "severity": 1, "message": "559", "line": 14, "column": 3, "nodeType": "540", "messageId": "541", "endLine": 14, "endColumn": 16}, {"ruleId": "538", "severity": 1, "message": "560", "line": 15, "column": 3, "nodeType": "540", "messageId": "541", "endLine": 15, "endColumn": 10}, {"ruleId": "538", "severity": 1, "message": "561", "line": 16, "column": 3, "nodeType": "540", "messageId": "541", "endLine": 16, "endColumn": 9}, {"ruleId": "538", "severity": 1, "message": "562", "line": 20, "column": 16, "nodeType": "540", "messageId": "541", "endLine": 20, "endColumn": 19}, {"ruleId": "538", "severity": 1, "message": "563", "line": 47, "column": 12, "nodeType": "540", "messageId": "541", "endLine": 47, "endColumn": 30}, {"ruleId": "538", "severity": 1, "message": "564", "line": 20, "column": 10, "nodeType": "540", "messageId": "541", "endLine": 20, "endColumn": 20}, {"ruleId": "538", "severity": 1, "message": "565", "line": 20, "column": 22, "nodeType": "540", "messageId": "541", "endLine": 20, "endColumn": 35}, {"ruleId": "538", "severity": 1, "message": "566", "line": 3, "column": 20, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 28}, {"ruleId": "538", "severity": 1, "message": "567", "line": 3, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 43}, {"ruleId": "538", "severity": 1, "message": "568", "line": 3, "column": 45, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 52}, {"ruleId": "538", "severity": 1, "message": "569", "line": 13, "column": 21, "nodeType": "540", "messageId": "541", "endLine": 13, "endColumn": 31}, {"ruleId": "542", "severity": 1, "message": "570", "line": 40, "column": 8, "nodeType": "544", "endLine": 40, "endColumn": 41, "suggestions": "571"}, {"ruleId": "538", "severity": 1, "message": "572", "line": 17, "column": 12, "nodeType": "540", "messageId": "541", "endLine": 17, "endColumn": 21}, {"ruleId": "538", "severity": 1, "message": "566", "line": 3, "column": 20, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 28}, {"ruleId": "542", "severity": 1, "message": "573", "line": 39, "column": 8, "nodeType": "544", "endLine": 39, "endColumn": 42, "suggestions": "574"}, {"ruleId": "538", "severity": 1, "message": "575", "line": 3, "column": 10, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 22}, {"ruleId": "542", "severity": 1, "message": "576", "line": 24, "column": 8, "nodeType": "544", "endLine": 24, "endColumn": 19, "suggestions": "577"}, {"ruleId": "542", "severity": 1, "message": "578", "line": 33, "column": 8, "nodeType": "544", "endLine": 33, "endColumn": 30, "suggestions": "579"}, {"ruleId": "542", "severity": 1, "message": "580", "line": 43, "column": 8, "nodeType": "544", "endLine": 43, "endColumn": 10, "suggestions": "581"}, {"ruleId": "538", "severity": 1, "message": "582", "line": 17, "column": 5, "nodeType": "540", "messageId": "541", "endLine": 17, "endColumn": 17}, {"ruleId": "538", "severity": 1, "message": "583", "line": 18, "column": 5, "nodeType": "540", "messageId": "541", "endLine": 18, "endColumn": 12}, {"ruleId": "538", "severity": 1, "message": "584", "line": 1, "column": 60, "nodeType": "540", "messageId": "541", "endLine": 1, "endColumn": 66}, {"ruleId": "538", "severity": 1, "message": "585", "line": 28, "column": 12, "nodeType": "540", "messageId": "541", "endLine": 28, "endColumn": 26}, {"ruleId": "538", "severity": 1, "message": "586", "line": 28, "column": 28, "nodeType": "540", "messageId": "541", "endLine": 28, "endColumn": 45}, {"ruleId": "542", "severity": 1, "message": "587", "line": 83, "column": 8, "nodeType": "544", "endLine": 83, "endColumn": 10, "suggestions": "588"}, {"ruleId": "538", "severity": 1, "message": "552", "line": 3, "column": 19, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 30}, {"ruleId": "538", "severity": 1, "message": "589", "line": 14, "column": 12, "nodeType": "540", "messageId": "541", "endLine": 14, "endColumn": 24}, {"ruleId": "538", "severity": 1, "message": "590", "line": 93, "column": 11, "nodeType": "540", "messageId": "541", "endLine": 93, "endColumn": 23}, {"ruleId": "538", "severity": 1, "message": "591", "line": 114, "column": 9, "nodeType": "540", "messageId": "541", "endLine": 114, "endColumn": 25}, {"ruleId": "538", "severity": 1, "message": "592", "line": 148, "column": 9, "nodeType": "540", "messageId": "541", "endLine": 148, "endColumn": 22}, {"ruleId": "538", "severity": 1, "message": "593", "line": 4, "column": 10, "nodeType": "540", "messageId": "541", "endLine": 4, "endColumn": 17}, {"ruleId": "538", "severity": 1, "message": "594", "line": 13, "column": 10, "nodeType": "540", "messageId": "541", "endLine": 13, "endColumn": 17}, {"ruleId": "538", "severity": 1, "message": "595", "line": 14, "column": 10, "nodeType": "540", "messageId": "541", "endLine": 14, "endColumn": 15}, {"ruleId": "538", "severity": 1, "message": "592", "line": 115, "column": 9, "nodeType": "540", "messageId": "541", "endLine": 115, "endColumn": 22}, {"ruleId": "538", "severity": 1, "message": "596", "line": 132, "column": 9, "nodeType": "540", "messageId": "541", "endLine": 132, "endColumn": 19}, {"ruleId": "538", "severity": 1, "message": "597", "line": 145, "column": 9, "nodeType": "540", "messageId": "541", "endLine": 145, "endColumn": 22}, {"ruleId": "542", "severity": 1, "message": "573", "line": 44, "column": 8, "nodeType": "544", "endLine": 44, "endColumn": 21, "suggestions": "598"}, {"ruleId": "542", "severity": 1, "message": "599", "line": 19, "column": 8, "nodeType": "544", "endLine": 19, "endColumn": 10, "suggestions": "600"}, {"ruleId": "538", "severity": 1, "message": "601", "line": 6, "column": 14, "nodeType": "540", "messageId": "541", "endLine": 6, "endColumn": 20}, {"ruleId": "538", "severity": 1, "message": "582", "line": 6, "column": 41, "nodeType": "540", "messageId": "541", "endLine": 6, "endColumn": 53}, {"ruleId": "538", "severity": 1, "message": "602", "line": 7, "column": 46, "nodeType": "540", "messageId": "541", "endLine": 7, "endColumn": 52}, {"ruleId": "538", "severity": 1, "message": "603", "line": 11, "column": 7, "nodeType": "540", "messageId": "541", "endLine": 11, "endColumn": 19}, {"ruleId": "538", "severity": 1, "message": "604", "line": 307, "column": 11, "nodeType": "540", "messageId": "541", "endLine": 307, "endColumn": 27}, {"ruleId": "538", "severity": 1, "message": "605", "line": 4, "column": 45, "nodeType": "540", "messageId": "541", "endLine": 4, "endColumn": 64}, {"ruleId": "538", "severity": 1, "message": "606", "line": 4, "column": 66, "nodeType": "540", "messageId": "541", "endLine": 4, "endColumn": 79}, {"ruleId": "538", "severity": 1, "message": "607", "line": 4, "column": 111, "nodeType": "540", "messageId": "541", "endLine": 4, "endColumn": 123}, {"ruleId": "542", "severity": 1, "message": "608", "line": 29, "column": 8, "nodeType": "544", "endLine": 29, "endColumn": 10, "suggestions": "609"}, {"ruleId": "538", "severity": 1, "message": "610", "line": 256, "column": 11, "nodeType": "540", "messageId": "541", "endLine": 256, "endColumn": 26}, {"ruleId": "542", "severity": 1, "message": "611", "line": 24, "column": 8, "nodeType": "544", "endLine": 24, "endColumn": 33, "suggestions": "612"}, {"ruleId": "538", "severity": 1, "message": "601", "line": 5, "column": 57, "nodeType": "540", "messageId": "541", "endLine": 5, "endColumn": 63}, {"ruleId": "542", "severity": 1, "message": "613", "line": 24, "column": 8, "nodeType": "544", "endLine": 24, "endColumn": 33, "suggestions": "614"}, {"ruleId": "538", "severity": 1, "message": "615", "line": 122, "column": 19, "nodeType": "540", "messageId": "541", "endLine": 122, "endColumn": 28}, {"ruleId": "538", "severity": 1, "message": "616", "line": 137, "column": 19, "nodeType": "540", "messageId": "541", "endLine": 137, "endColumn": 22}, {"ruleId": "538", "severity": 1, "message": "617", "line": 4, "column": 5, "nodeType": "540", "messageId": "541", "endLine": 4, "endColumn": 14}, {"ruleId": "538", "severity": 1, "message": "618", "line": 6, "column": 5, "nodeType": "540", "messageId": "541", "endLine": 6, "endColumn": 10}, {"ruleId": "538", "severity": 1, "message": "556", "line": 7, "column": 5, "nodeType": "540", "messageId": "541", "endLine": 7, "endColumn": 15}, {"ruleId": "538", "severity": 1, "message": "619", "line": 8, "column": 5, "nodeType": "540", "messageId": "541", "endLine": 8, "endColumn": 16}, {"ruleId": "538", "severity": 1, "message": "620", "line": 57, "column": 9, "nodeType": "540", "messageId": "541", "endLine": 57, "endColumn": 26}, {"ruleId": "538", "severity": 1, "message": "555", "line": 2, "column": 10, "nodeType": "540", "messageId": "541", "endLine": 2, "endColumn": 24}, {"ruleId": "538", "severity": 1, "message": "621", "line": 2, "column": 26, "nodeType": "540", "messageId": "541", "endLine": 2, "endColumn": 34}, {"ruleId": "538", "severity": 1, "message": "622", "line": 3, "column": 35, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 41}, {"ruleId": "538", "severity": 1, "message": "623", "line": 5, "column": 5, "nodeType": "540", "messageId": "541", "endLine": 5, "endColumn": 15}, {"ruleId": "538", "severity": 1, "message": "554", "line": 6, "column": 5, "nodeType": "540", "messageId": "541", "endLine": 6, "endColumn": 18}, {"ruleId": "542", "severity": 1, "message": "624", "line": 29, "column": 8, "nodeType": "544", "endLine": 29, "endColumn": 31, "suggestions": "625"}, {"ruleId": "542", "severity": 1, "message": "573", "line": 31, "column": 8, "nodeType": "544", "endLine": 31, "endColumn": 17, "suggestions": "626"}, {"ruleId": "538", "severity": 1, "message": "627", "line": 3, "column": 33, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 38}, {"ruleId": "538", "severity": 1, "message": "628", "line": 14, "column": 12, "nodeType": "540", "messageId": "541", "endLine": 14, "endColumn": 21}, {"ruleId": "542", "severity": 1, "message": "629", "line": 29, "column": 8, "nodeType": "544", "endLine": 29, "endColumn": 10, "suggestions": "630"}, {"ruleId": "538", "severity": 1, "message": "631", "line": 3, "column": 41, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 62}, {"ruleId": "538", "severity": 1, "message": "631", "line": 3, "column": 40, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 61}, {"ruleId": "542", "severity": 1, "message": "632", "line": 28, "column": 8, "nodeType": "544", "endLine": 28, "endColumn": 24, "suggestions": "633"}, {"ruleId": "538", "severity": 1, "message": "583", "line": 3, "column": 51, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 58}, {"ruleId": "538", "severity": 1, "message": "618", "line": 3, "column": 102, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 107}, {"ruleId": "538", "severity": 1, "message": "634", "line": 18, "column": 12, "nodeType": "540", "messageId": "541", "endLine": 18, "endColumn": 21}, {"ruleId": "542", "severity": 1, "message": "635", "line": 23, "column": 8, "nodeType": "544", "endLine": 23, "endColumn": 17, "suggestions": "636"}, {"ruleId": "538", "severity": 1, "message": "637", "line": 3, "column": 40, "nodeType": "540", "messageId": "541", "endLine": 3, "endColumn": 46}, {"ruleId": "542", "severity": 1, "message": "638", "line": 30, "column": 8, "nodeType": "544", "endLine": 30, "endColumn": 10, "suggestions": "639"}, {"ruleId": "538", "severity": 1, "message": "640", "line": 5, "column": 5, "nodeType": "540", "messageId": "541", "endLine": 5, "endColumn": 11}, {"ruleId": "542", "severity": 1, "message": "641", "line": 39, "column": 8, "nodeType": "544", "endLine": 39, "endColumn": 16, "suggestions": "642"}, "no-unused-vars", "'LeagueSelection' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeError'. Either include it or remove the dependency array.", "ArrayExpression", ["643"], "The 'publicRoutes' array makes the dependencies of useCallback Hook (at line 97) change on every render. To fix this, wrap the initialization of 'publicRoutes' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'setUserData'. Either include it or remove the dependency array.", ["644"], "React Hook useEffect has a missing dependency: 'publicRoutes'. Either include it or remove the dependency array.", ["645"], "'FaChartLine' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaCalendarAlt' is defined but never used.", "'FaFootballBall' is defined but never used.", "'FaChartBar' is defined but never used.", "'FaExchangeAlt' is defined but never used.", "'FaEye' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaClock' is defined but never used.", "'FaBell' is defined but never used.", "'Bar' is defined but never used.", "'teamPopularityData' is assigned a value but never used.", "'challenges' is assigned a value but never used.", "'setChallenges' is assigned a value but never used.", "'FaFilter' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'setSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllBets'. Either include it or remove the dependency array.", ["646"], "'otpExpiry' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLeaderboard'. Either include it or remove the dependency array.", ["647"], "'API_BASE_URL' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchConversations'. Either include it or remove the dependency array.", ["648"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'markMessagesAsRead'. Either include them or remove the dependency array.", ["649"], "React Hook useEffect has a missing dependency: 'checkChallengeStatus'. Either include it or remove the dependency array.", ["650"], "'FaInfoCircle' is defined but never used.", "'FaTimes' is defined but never used.", "'useRef' is defined but never used.", "'payoutPreviews' is assigned a value but never used.", "'setPayoutPreviews' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'fetchLeagueDetails'. Either include it or remove the dependency array.", ["651"], "'loadingUsers' is assigned a value but never used.", "'currentUsers' is assigned a value but never used.", "'renderPagination' is assigned a value but never used.", "'calculateOdds' is assigned a value but never used.", "'FaCoins' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", "'getUserStatus' is assigned a value but never used.", ["652"], "React Hook useEffect has a missing dependency: 'fetchPendingRequests'. Either include it or remove the dependency array.", ["653"], "'FaStar' is defined but never used.", "'FaFire' is defined but never used.", "'API_BASE_URL' is assigned a value but never used.", "'renderNavigation' is assigned a value but never used.", "'FaExclamationCircle' is defined but never used.", "'FaUserFriends' is defined but never used.", "'FaUserCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFriends'. Either include it or remove the dependency array.", ["654"], "'handleChallenge' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["655"], "React Hook useEffect has a missing dependency: 'fetchCreditRequests'. Either include it or remove the dependency array.", ["656"], "'matchDate' is assigned a value but never used.", "'now' is assigned a value but never used.", "'FaGamepad' is defined but never used.", "'FaCog' is defined but never used.", "'FaMoneyBill' is defined but never used.", "'handleLogoutClick' is assigned a value but never used.", "'FaTrophy' is defined but never used.", "'FaSave' is defined but never used.", "'FaDownload' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReport'. Either include it or remove the dependency array.", ["657"], ["658"], "'FaKey' is defined but never used.", "'secretKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initiate2FASetup'. Either include it or remove the dependency array.", ["659"], "'FaExclamationTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendInitialOTP'. Either include it or remove the dependency array.", ["660"], "'adminInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPreferences'. Either include it or remove the dependency array.", ["661"], "'FaCopy' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["662"], "'FaUser' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchUserBets', 'fetchUserDetails', and 'fetchUserTransactions'. Either include them or remove the dependency array.", ["663"], {"desc": "664", "fix": "665"}, {"desc": "666", "fix": "667"}, {"desc": "668", "fix": "669"}, {"desc": "670", "fix": "671"}, {"desc": "672", "fix": "673"}, {"desc": "674", "fix": "675"}, {"desc": "676", "fix": "677"}, {"desc": "678", "fix": "679"}, {"desc": "680", "fix": "681"}, {"desc": "682", "fix": "683"}, {"desc": "684", "fix": "685"}, {"desc": "686", "fix": "687"}, {"desc": "688", "fix": "689"}, {"desc": "690", "fix": "691"}, {"desc": "692", "fix": "693"}, {"desc": "694", "fix": "695"}, {"desc": "696", "fix": "697"}, {"desc": "698", "fix": "699"}, {"desc": "700", "fix": "701"}, {"desc": "702", "fix": "703"}, {"desc": "704", "fix": "705"}, "Update the dependencies array to be: [removeError]", {"range": "706", "text": "707"}, "Update the dependencies array to be: [token, userId, setUserData, navigate, location.pathname]", {"range": "708", "text": "709"}, "Update the dependencies array to be: [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", {"range": "710", "text": "711"}, "Update the dependencies array to be: [pagination.currentPage, filters, fetchAllBets]", {"range": "712", "text": "713"}, "Update the dependencies array to be: [pagination.current_page, filters, fetchLeaderboard]", {"range": "714", "text": "715"}, "Update the dependencies array to be: [activeTab, fetchConversations]", {"range": "716", "text": "717"}, "Update the dependencies array to be: [fetchMessages, markMessagesAsRead, selectedConversation]", {"range": "718", "text": "719"}, "Update the dependencies array to be: [checkChallengeStatus]", {"range": "720", "text": "721"}, "Update the dependencies array to be: [fetchLeagueDetails]", {"range": "722", "text": "723"}, "Update the dependencies array to be: [currentPage, fetchLeaderboard]", {"range": "724", "text": "725"}, "Update the dependencies array to be: [fetchPendingRequests]", {"range": "726", "text": "727"}, "Update the dependencies array to be: [fetchFriends]", {"range": "728", "text": "729"}, "Update the dependencies array to be: [navigate, currentUserId, fetchInitialData]", {"range": "730", "text": "731"}, "Update the dependencies array to be: [navigate, currentUserId, fetchCreditRequests]", {"range": "732", "text": "733"}, "Update the dependencies array to be: [reportType, dateRange, fetchReport]", {"range": "734", "text": "735"}, "Update the dependencies array to be: [fetchLeaderboard, filters]", {"range": "736", "text": "737"}, "Update the dependencies array to be: [initiate2FASetup]", {"range": "738", "text": "739"}, "Update the dependencies array to be: [initialOtpSent, sendInitialOTP]", {"range": "740", "text": "741"}, "Update the dependencies array to be: [adminId, fetchPreferences]", {"range": "742", "text": "743"}, "Update the dependencies array to be: [fetchAdminData]", {"range": "744", "text": "745"}, "Update the dependencies array to be: [fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", {"range": "746", "text": "747"}, [985, 987], "[removeError]", [2571, 2615], "[token, userId, setUserData, navigate, location.pathname]", [4000, 4079], "[token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", [1144, 1177], "[pagination.currentPage, filters, fetchAllBets]", [1249, 1283], "[pagination.current_page, filters, fetchLeaderboard]", [1116, 1127], "[activeTab, fetchConversations]", [1474, 1496], "[fetch<PERSON>essages, markMessagesAsRead, selectedConversation]", [1532, 1534], "[checkChallengeStatus]", [3030, 3032], "[fetchLeagueDetails]", [1160, 1173], "[currentPage, fetchLeaderboard]", [696, 698], "[fetchPendingRequests]", [1331, 1333], "[fetchFriends]", [856, 881], "[navigate, currentUserId, fetchInitialData]", [891, 916], "[navigate, currentUserId, fetchCreditRequests]", [864, 887], "[reportType, dateRange, fetchReport]", [887, 896], "[fetchLeaderboard, filters]", [1137, 1139], "[initiate2FASetup]", [1264, 1280], "[initialOtpSent, sendInitialOTP]", [856, 865], "[adminId, fetchPreferences]", [1269, 1271], "[fetchAdminData]", [1020, 1028], "[fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]"]