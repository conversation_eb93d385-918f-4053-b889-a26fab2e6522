/* User Management Responsive Table Styles */
.user-table-container {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-table {
    min-width: 100%;
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.user-table thead {
    background: #2563eb;
}

.user-table th,
.user-table td {
    text-align: left;
    vertical-align: middle;
    border-bottom: 1px solid #e5e7eb;
}

.user-table tbody tr:hover {
    background-color: #f9fafb;
}

.user-table tbody tr:hover .user-table-actions {
    background: #f3f4f6;
}

/* Actions column styling */
.user-table-actions {
    position: sticky;
    right: 0;
    background: white;
    z-index: 10;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.05);
    min-width: 140px;
    width: 140px;
}

/* Sports Card Modal Styles */
.sports-card-modal {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    overflow: hidden;
}

.sports-card-header {
    background: linear-gradient(135deg, #166534 0%, #15803d 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.sports-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/><circle cx="50" cy="50" r="25" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></svg>') center/cover;
    opacity: 0.3;
}

.sports-card-avatar {
    position: relative;
    z-index: 2;
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.sports-card-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
}

.sports-card-name {
    position: relative;
    z-index: 2;
    font-size: 1.75rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sports-card-status {
    position: relative;
    z-index: 2;
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.sports-card-body {
    padding: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #166534;
}

.stat-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #166534;
}

.stat-card-icon {
    color: #166534;
    font-size: 1.2rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #6b7280;
    font-weight: 500;
}

.stat-value {
    font-weight: 600;
    color: #111827;
}

.stat-value.positive {
    color: #059669;
}

.stat-value.negative {
    color: #dc2626;
}

.stat-value.neutral {
    color: #d97706;
}

/* Performance Stats Grid */
.performance-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin: 1.5rem 0;
}

.performance-stat {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.performance-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.performance-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Responsive Design */
@media (min-width: 1024px) and (max-width: 1366px) {
    .user-table-hide-medium {
        display: none !important;
    }

    .user-table {
        min-width: 700px;
    }

    .user-table-actions {
        min-width: 120px;
        width: 120px;
    }
}

@media (max-width: 1024px) {
    .user-table-hide-small,
    .user-table-hide-medium {
        display: none !important;
    }

    .user-table {
        min-width: 500px;
    }

    .user-table-actions {
        min-width: 100px;
        width: 100px;
    }

    .performance-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .user-table-hide-very-small,
    .user-table-hide-small,
    .user-table-hide-medium {
        display: none !important;
    }

    .user-table {
        min-width: 400px;
    }

    .user-table-actions {
        min-width: 80px;
        width: 80px;
    }

    .sports-card-modal {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }

    .sports-card-header {
        padding: 1.5rem;
    }

    .sports-card-avatar {
        width: 100px;
        height: 100px;
    }

    .sports-card-name {
        font-size: 1.5rem;
    }

    .sports-card-body {
        padding: 1.5rem;
    }

    .performance-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

/* Ensure Actions column is always visible and sticky */
@media (max-width: 1600px) {
    .user-table-actions {
        position: sticky;
        right: 0;
        background: white;
        z-index: 10;
    }
}

/* Loading animation for modal */
.loading-spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #166534;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
